# 余额不足处理逻辑测试说明

## 修改内容总结

### 1. 配置修改
- 修正了 `nonRetryableErrors` 配置中的大小写问题
- 将 `"Insufficient hot wallet balance"` 改为 `"insufficient hot wallet balance"`

### 2. 处理逻辑修改
- 添加了 `isInsufficientBalance` 变量来专门检测余额不足错误
- 当检测到余额不足时，设置 `finalState = 2`（processing_status=2）
- 不将余额不足的订单移入死信队列，直接处理并更新状态

### 3. 状态更新修改
- 扩展了 `StatusUpdateMessage` 结构，添加 `AutoWithdrawalProgress` 字段
- 添加了 `PushStatusUpdateToRedisWithProgress` 函数
- 修改了 gRPC 更新器以支持设置 `auto_withdrawal_progress=0`

## 预期行为

当遇到余额不足错误时：

1. **错误检测**：
   - 检测到错误信息包含 "insufficient" 和 "balance" 关键词
   - 设置 `isInsufficientBalance = true`

2. **状态设置**：
   - `finalState = 2`（对应 processing_status=2，表示"处理中(待冷钱包转入)"）
   - `auto_withdrawal_progress = 0`（表示"未开始"）

3. **订单处理**：
   - 不进行重试
   - 不移入死信队列
   - 直接从本地队列移除
   - 推送状态更新到 Redis 队列

4. **远程状态更新**：
   - gRPC 更新器接收到状态更新消息
   - 调用远程接口更新订单状态
   - 设置 `processing_status=2` 和 `auto_withdrawal_progress=0`

## 测试场景

### 场景1：ETH 余额不足
```
错误信息：insufficient hot wallet balance for ETH: Have 0.5, Need 1.0
预期结果：processing_status=2, auto_withdrawal_progress=0
```

### 场景2：USDT (ERC20) 余额不足
```
错误信息：insufficient hot wallet balance for USDT: Have 100, Need 1000
预期结果：processing_status=2, auto_withdrawal_progress=0
```

### 场景3：TRX 余额不足
```
错误信息：insufficient TRX balance: Have 50 TRX, Need 150 TRX (Amount: 50 + Reserve: 100)
预期结果：processing_status=2, auto_withdrawal_progress=0
```

### 场景4：TRC20 手续费不足
```
错误信息：insufficient TRX balance for fees: Have 50 TRX, Need at least 100 TRX
预期结果：processing_status=2, auto_withdrawal_progress=0
```

## 验证方法

1. **日志检查**：
   - 查看是否有 "Insufficient balance detected" 日志
   - 查看是否有 "Pushing status update for insufficient balance" 日志

2. **数据库检查**：
   - 确认 `processing_status` 字段更新为 2
   - 确认 `auto_withdrawal_progress` 字段更新为 0

3. **队列检查**：
   - 确认订单不在处理队列中
   - 确认订单不在死信队列中
   - 确认状态更新消息在状态更新队列中

## 注意事项

- 修改后的逻辑只对包含 "insufficient" 和 "balance" 关键词的错误生效
- 其他类型的错误仍按原有逻辑处理（重试或失败）
- 需要确保远程服务支持 `auto_withdrawal_progress` 字段的更新
