# 商户提现模块运行指南

## 系统架构

商户提现模块已集成到主提现系统中，支持处理：
- 用户提现 (user_withdraws)
- 商户提现 (merchant_withdraws) 
- 商户结算 (merchant_settlements)

## 配置说明

### 1. 启用商户提现功能

在 `manifest/config/config.yaml` 中配置：

```yaml
withdrawalProcessor:
  enabled: true       # 启用提现处理任务
  spec: "*/10 * * * * *" # 每10秒执行一次
  batchSize: 50         
  
  transactionTypes:
    userWithdrawals:
      enabled: true
      batchSize: 50
    merchantWithdrawals:
      enabled: true       # 启用商户提现处理
      batchSize: 30      # 每批处理30条
    merchantSettlements:
      enabled: true       # 启用商户结算处理
      batchSize: 20      # 每批处理20条
```

### 2. 商户状态更新配置

```yaml
grpcUpdater:
  # 商户状态更新配置
  merchantRedisQueueName: "queue:merchant_status_update" 
  merchantDLQName: "queue:merchant_status_update_dlq"
  merchantTimeout: 5        # 超时时间(秒)
  merchantMaxRetries: 3     # 最大重试次数
```

## 运行步骤

### 1. 编译项目

```bash
go build -o task-withdraw .
```

### 2. 启动系统

```bash
./task-withdraw task
```

系统会自动启动以下组件：
- **Enhanced Transaction Processor**: 定时从gRPC服务器拉取待处理的交易
- **Withdrawal Consumer Worker**: 处理Redis队列中的交易
- **Merchant Status Worker**: 处理商户状态更新队列
- **gRPC Status Updater**: 更新用户提现状态

## 处理流程

### 商户提现处理流程：

1. **拉取订单**: Enhanced Processor 每10秒从gRPC服务器拉取 `state=2` 的商户提现订单
2. **入队**: 将订单封装为 `TransactionRequest` 推送到 Redis 队列
3. **处理**: Consumer Worker 从队列取出订单，转换为用户提现格式处理
4. **状态更新**: 处理完成后，推送状态更新到商户状态队列
5. **更新数据库**: Merchant Status Worker 调用gRPC更新 `merchant_withdraws` 表

### 状态映射：

商户状态 → 处理状态：
- state=1 (待审核) → 不处理
- state=2 (处理中) → 开始自动处理
- state=3 (已拒绝) → 不处理
- state=4 (已完成) → 交易成功
- state=5 (失败) → 交易失败

## 监控与调试

### 查看日志

```bash
tail -f logs/$(date +%Y-%m-%d).log
```

### 关键日志标识：

- `[EnhancedTransactionProcessor]`: 主处理器日志
- `[MerchantWithdrawals]`: 商户提现处理日志
- `[MerchantSettlements]`: 商户结算处理日志
- `[MerchantStatusWorker]`: 商户状态更新工作线程日志
- `[ProcessMerchantStatusUpdate]`: 商户状态更新处理日志

### Redis 队列监控

查看待处理的商户交易：
```bash
redis-cli -n 3
> LLEN queue:transaction_processing
```

查看商户状态更新队列：
```bash
> LLEN queue:merchant_status_update
```

查看死信队列：
```bash
> LLEN queue:merchant_status_update_dlq
```

## 常见问题

### 1. 商户提现订单没有被处理

检查：
- 配置中 `merchantWithdrawals.enabled` 是否为 `true`
- gRPC服务器是否可访问
- 订单状态是否为 `state=2`

### 2. 商户订单一直处于处理中

检查：
- Merchant Status Worker 是否正常运行
- Redis 队列 `queue:merchant_status_update` 是否有积压
- gRPC 更新接口是否正常

### 3. 余额不足的订单处理

商户提现余额不足时：
- 订单保持 `state=2` 状态
- 下次拉取时会重新尝试
- 不会进入死信队列

## 测试方法

### 1. 创建测试商户提现订单

在数据库中插入测试数据：
```sql
INSERT INTO merchant_withdraws (
    merchant_id, state, amount, address, token_id
) VALUES (
    1, 2, 100, '0xTestAddress', 1
);
```

### 2. 观察处理过程

查看日志确认：
1. Enhanced Processor 拉取到订单
2. 订单被推送到Redis队列
3. Consumer处理订单
4. 状态更新被推送到商户队列
5. 最终更新到数据库

## 性能优化

### 调整批处理大小

根据系统负载调整：
```yaml
merchantWithdrawals:
  batchSize: 50  # 增加批处理大小
```

### 调整处理频率

```yaml
withdrawalProcessor:
  spec: "*/5 * * * * *"  # 改为每5秒执行
```

### 增加并发Worker

```yaml
withdrawalConsumer:
  concurrency: 3  # 启动3个并发Worker
```

## 安全注意事项

1. 确保私钥安全存储
2. 定期检查死信队列中的失败交易
3. 监控异常的大额提现
4. 保持日志审计跟踪

## 维护建议

1. 定期清理死信队列中已处理的消息
2. 监控Redis内存使用
3. 定期备份配置文件
4. 保持gRPC服务的高可用性