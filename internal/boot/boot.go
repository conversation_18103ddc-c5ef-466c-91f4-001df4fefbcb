// Package boot 包含应用程序的引导初始化逻辑
package boot

import (
	"context"
	"task-withdraw/internal/config"

	"github.com/gogf/gf/v2/os/glog"
)

// Initialize 初始化应用程序所有组件
func Initialize(ctx context.Context) {
	// 初始化日志输出
	glog.Info(ctx, "应用程序开始初始化...")

	// 初始化系统配置（使用 XPay Config）
	if err := config.Initialize(ctx); err != nil {
		glog.Errorf(ctx, "初始化系统配置失败: %v", err)
		panic(err)
	}
	glog.Info(ctx, "应用程序初始化完成")
}
