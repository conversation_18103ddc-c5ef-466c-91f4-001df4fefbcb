package task

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials" // Import needed for PerRPCCredentials interface
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata" // Import needed for metadata.MD type

	// 假设 go.mod 模块名为 task-withdraw
	// 且 proto 文件中 go_package 为 task-server/api/task/v1;taskv1
	// 但实际生成在 task-withdraw/api 下
	taskv1 "task-withdraw/api"
	"task-withdraw/internal/service" // Import service package
)

const (
	defaultTimeout = 5 * time.Second
	// 业务成功码，假设 gRPC 服务端遵循此约定
	grpcSuccessCode = 200
	// Configuration keys for gRPC client
	// Assuming these keys exist in config.yaml under a suitable section like 'grpcClient.taskService'
	// Adjust these keys if your config structure is different.
	// Based on processor.go, it seems config is loaded into a struct first.
	// We'll use keys that likely correspond to that struct's fields.
	// Pointing to the actual config keys found in config.yaml under grpc.client.user-service
	grpcConfigEndpointsKey = "grpc.client.user-service.endpoints"
	grpcConfigAPIKeyKey    = "grpc.client.user-service.apiKeys"
)

// Ensure WithdrawalClient implements the service interface.
var _ service.IWithdrawalClient = (*WithdrawalClient)(nil)

// WithdrawalClient 封装了与提现 gRPC 服务交互的客户端。
type WithdrawalClient struct {
	client taskv1.TaskServiceClient
	conn   *grpc.ClientConn
	mu     sync.Mutex // 用于保护并发访问（如果需要）
}

// apiKeyCredentials implements credentials.PerRPCCredentials for adding API key to metadata.
type apiKeyCredentials struct {
	apiKey string
}

// GetRequestMetadata gets the current request metadata.
func (c apiKeyCredentials) GetRequestMetadata(ctx context.Context, uri ...string) (map[string]string, error) {
	// TODO: Consider using a more standard key like "authorization" with "Bearer <apiKey>" if the server expects that.
	// Ensure metadata key is lowercase as per gRPC convention.
	return map[string]string{
		"x-api-key": c.apiKey,
	}, nil
}

// RequireTransportSecurity indicates whether transport security is required.
// Returning false as we are using insecure transport for now.
func (c apiKeyCredentials) RequireTransportSecurity() bool {
	return false
}

// init registers the WithdrawalClient implementation using the New constructor.
func init() {
	service.RegisterWithdrawalClient(New())
}

// New creates a new WithdrawalClient instance by reading configuration.
// It panics if essential configuration is missing or connection fails.
func New() service.IWithdrawalClient {
	ctx := gctx.New() // Use a background context for initialization
	logPrefix := "[WithdrawalClient]"

	// Read configuration using g.Cfg()
	endpointsVar := g.Cfg().MustGet(ctx, grpcConfigEndpointsKey)
	apiKey := g.Cfg().MustGet(ctx, grpcConfigAPIKeyKey).String() // API Key is likely a single string

	if endpointsVar.IsNil() || len(endpointsVar.Strings()) == 0 {
		panic(fmt.Sprintf("%s gRPC server endpoints configuration ('%s') is missing or empty", logPrefix, grpcConfigEndpointsKey))
	}

	// For simplicity, use the first endpoint. Consider load balancing if multiple endpoints are provided.
	grpcServerAddress := endpointsVar.Strings()[0]
	if grpcServerAddress == "" {
		panic(fmt.Sprintf("%s gRPC server address configuration ('%s') is empty", logPrefix, grpcConfigEndpointsKey))
	}

	// --- Connection Logic (adapted from NewWithdrawalClient) ---
	address := strings.TrimPrefix(grpcServerAddress, "http://")
	address = strings.TrimPrefix(address, "https://")

	dialCtx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	apiKeyCreds := apiKeyCredentials{apiKey: apiKey}
	var _ credentials.PerRPCCredentials = apiKeyCreds
	var _ metadata.MD

	dialOpts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
	}
	if apiKey != "" {
		dialOpts = append(dialOpts, grpc.WithPerRPCCredentials(apiKeyCreds))
		g.Log().Infof(ctx, "%s API Key provided, enabling PerRPCCredentials.", logPrefix)
	} else {
		g.Log().Warningf(ctx, "%s API Key not provided, connecting without PerRPCCredentials.", logPrefix)
	}

	conn, err := grpc.DialContext(
		dialCtx,
		address,
		dialOpts...,
	)
	if err != nil {
		// Panic during initialization if connection fails
		panic(gerror.Wrapf(err, "%s failed to connect to gRPC server (%s) during initialization", logPrefix, address))
	}

	grpcClient := taskv1.NewTaskServiceClient(conn)
	g.Log().Infof(ctx, "%s Successfully connected to TaskService gRPC server: %s", logPrefix, address)

	client := &WithdrawalClient{
		client: grpcClient,
		conn:   conn,
	}

	return client
}

// Close 关闭 gRPC 连接。
func (c *WithdrawalClient) Close() error {
	if c.conn != nil {
		g.Log().Infof(gctx.New(), "[WithdrawalClient] Closing gRPC connection...")
		return c.conn.Close()
	}
	return nil
}

// FetchPendingWithdrawals 从 gRPC 服务获取待处理的提现订单（默认获取状态1）。
func (c *WithdrawalClient) FetchPendingWithdrawals(ctx context.Context, batchSize int) ([]*taskv1.Withdrawal, error) {
	// 默认获取状态1的订单
	return c.FetchPendingWithdrawalsWithStatus(ctx, batchSize, 1)
}

// FetchPendingWithdrawalsWithStatus 从 gRPC 服务获取指定状态的提现订单。
func (c *WithdrawalClient) FetchPendingWithdrawalsWithStatus(ctx context.Context, batchSize int, processingStatus int32) ([]*taskv1.Withdrawal, error) {
	logPrefix := fmt.Sprintf("[WithdrawalClient.FetchPendingWithdrawalsWithStatus(status=%d)]", processingStatus)
	if ctx == nil {
		ctx = gctx.New()
	}
	if batchSize <= 0 {
		batchSize = 10 // Default batch size if invalid
	}

	req := &taskv1.ListWithdrawalsRequest{
		PageSize:               int32(batchSize),
		Page:                   1,                // 获取第一页
		FilterProcessingStatus: processingStatus, // 获取指定状态的提现
		// 注意：这样设置可以避免拉取已完成的订单（status=4）
		// 服务端会自动过滤 auto_withdrawal_progress=0 的记录
		// 并在返回前将其更新为 auto_withdrawal_progress=1（处理中）
		// 这样可以避免重复处理同一条记录
		// 也可以添加 audit_status 过滤，比如只获取已审核通过的
		// FilterAuditStatus: 3, // 3=审核通过
	}

	callCtx, cancel := context.WithTimeout(ctx, defaultTimeout) // 为 RPC 调用设置超时
	defer cancel()

	g.Log().Debugf(callCtx, "%s Calling ListWithdrawals with request: %+v", logPrefix, req)
	resp, err := c.client.ListWithdrawals(callCtx, req)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s gRPC call ListWithdrawals failed", logPrefix)
	}
	g.Log().Debugf(callCtx, "%s Received ListWithdrawals response: %+v", logPrefix, resp)

	// 检查业务状态码
	if resp.Code != grpcSuccessCode {
		return nil, gerror.Newf("%s ListWithdrawals failed with business code %d: %s", logPrefix, resp.Code, resp.Message)
	}

	// Check the oneof data_payload field
	listWithdrawalsData, ok := resp.GetDataPayload().(*taskv1.ApiResponse_ListWithdrawalsData)
	if !ok || listWithdrawalsData == nil || listWithdrawalsData.ListWithdrawalsData == nil {
		// Handle cases where data_payload is not the expected type or is nil
		// This could be a server error or an unexpected response structure.
		// Log the raw response for debugging.
		g.Log().Errorf(callCtx, "%s ListWithdrawals response data_payload is not ListWithdrawalsResponse or is nil. Received response: %+v", logPrefix, resp)
		return []*taskv1.Withdrawal{}, gerror.Newf("%s unexpected response data format from server", logPrefix)
	}

	// Extract the ListWithdrawalsResponse from the oneof field
	listResp := listWithdrawalsData.ListWithdrawalsData

	// Check if the extracted list is nil (though the check above should cover this)
	if listResp.Withdrawals == nil {
		g.Log().Warningf(callCtx, "%s ListWithdrawals response contains nil withdrawals list, returning empty list", logPrefix)
		return []*taskv1.Withdrawal{}, nil
	}

	g.Log().Infof(callCtx, "%s Successfully fetched %d pending withdrawals (Total: %d)", logPrefix, len(listResp.Withdrawals), listResp.TotalCount)
	return listResp.Withdrawals, nil
}

// UpdateWithdrawalStatus 通过 gRPC 更新提现订单的状态。
func (c *WithdrawalClient) UpdateWithdrawalStatus(ctx context.Context, req *taskv1.UpdateWithdrawalStatusRequest) error {
	logPrefix := "[WithdrawalClient.UpdateWithdrawalStatus]"
	if ctx == nil {
		ctx = gctx.New()
	}
	if req == nil {
		return gerror.Newf("%s update request cannot be nil", logPrefix)
	}
	if req.WithdrawalId <= 0 {
		return gerror.Newf("%s withdrawal_id must be positive", logPrefix)
	}

	callCtx, cancel := context.WithTimeout(ctx, defaultTimeout) // 为 RPC 调用设置超时
	defer cancel()

	g.Log().Debugf(callCtx, "%s Calling UpdateWithdrawalStatus with request: %+v", logPrefix, req)
	resp, err := c.client.UpdateWithdrawalStatus(callCtx, req)
	if err != nil {
		return gerror.Wrapf(err, "%s gRPC call UpdateWithdrawalStatus failed", logPrefix)
	}
	g.Log().Debugf(callCtx, "%s Received UpdateWithdrawalStatus response: %+v", logPrefix, resp)

	// 检查业务状态码
	if resp.Code != grpcSuccessCode {
		// 记录详细错误信息
		errMsg := fmt.Sprintf("%s UpdateWithdrawalStatus failed for withdrawal_id %d with business code %d: %s",
			logPrefix, req.WithdrawalId, resp.Code, resp.Message)
		g.Log().Errorf(callCtx, errMsg) // 使用 Errorf 记录错误
		return gerror.New(errMsg)       // 返回包含详细信息的错误
	}

	// Check the oneof data_payload field for success confirmation
	// According to the proto, success without data uses ApiResponse_SuccessNoData
	if _, ok := resp.GetDataPayload().(*taskv1.ApiResponse_SuccessNoData); !ok {
		// This case indicates a successful business code but unexpected data payload.
		// Log a warning, but still consider it a success based on the business code.
		// Depending on strictness requirements, this could be an error.
		g.Log().Warningf(callCtx, "%s UpdateWithdrawalStatus response has success code %d but unexpected data_payload type. Received response: %+v", logPrefix, resp.Code, resp)
		// We will proceed as if successful based on the code.
	}

	g.Log().Infof(callCtx, "%s Successfully updated status for withdrawal_id %d (ProcessingStatus: %d, AuditStatus: %d, AutoWithdrawalProgress: %d)",
		logPrefix, req.WithdrawalId, req.ProcessingStatus, req.AuditStatus, req.AutoWithdrawalProgress)
	return nil
}

// === Merchant Withdrawal Methods ===

// FetchPendingMerchantWithdrawals 从 gRPC 服务获取待处理的商户提现订单。
func (c *WithdrawalClient) FetchPendingMerchantWithdrawals(ctx context.Context, batchSize int) ([]*taskv1.MerchantWithdraw, error) {
	logPrefix := "[WithdrawalClient.FetchPendingMerchantWithdrawals]"
	if ctx == nil {
		ctx = gctx.New()
	}
	if batchSize <= 0 {
		batchSize = 10 // Default batch size if invalid
	}

	req := &taskv1.ListMerchantWithdrawsRequest{
		PageSize:             int32(batchSize),
		Page:                 1, // 获取第一页
		FilterState:          2, // 只获取处理中状态的提现 (2-处理中，已审核通过待自动处理)
		FilterWithdrawsType:  0, // 不过滤提现类型
		FilterMerchantId:     0, // 不过滤商户ID
		FilterOrderNo:        "", // 不过滤订单号
	}

	callCtx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	g.Log().Debugf(callCtx, "%s Calling ListMerchantWithdraws with request: %+v", logPrefix, req)
	resp, err := c.client.ListMerchantWithdraws(callCtx, req)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s gRPC call ListMerchantWithdraws failed", logPrefix)
	}
	g.Log().Debugf(callCtx, "%s Received ListMerchantWithdraws response: %+v", logPrefix, resp)

	// 检查业务状态码
	if resp.Code != grpcSuccessCode {
		return nil, gerror.Newf("%s ListMerchantWithdraws failed with business code %d: %s", logPrefix, resp.Code, resp.Message)
	}

	// Check the oneof data_payload field
	listMerchantWithdrawsData, ok := resp.GetDataPayload().(*taskv1.ApiResponse_ListMerchantWithdrawsData)
	if !ok || listMerchantWithdrawsData == nil || listMerchantWithdrawsData.ListMerchantWithdrawsData == nil {
		g.Log().Errorf(callCtx, "%s ListMerchantWithdraws response data_payload is not ListMerchantWithdrawsResponse or is nil. Received response: %+v", logPrefix, resp)
		return []*taskv1.MerchantWithdraw{}, gerror.Newf("%s unexpected response data format from server", logPrefix)
	}

	// Extract the ListMerchantWithdrawsResponse from the oneof field
	listResp := listMerchantWithdrawsData.ListMerchantWithdrawsData

	// Check if the extracted list is nil
	if listResp.MerchantWithdraws == nil {
		g.Log().Warningf(callCtx, "%s ListMerchantWithdraws response contains nil merchant withdraws list, returning empty list", logPrefix)
		return []*taskv1.MerchantWithdraw{}, nil
	}

	g.Log().Infof(callCtx, "%s Successfully fetched %d pending merchant withdrawals (Total: %d)", logPrefix, len(listResp.MerchantWithdraws), listResp.TotalCount)
	return listResp.MerchantWithdraws, nil
}

// UpdateMerchantWithdrawStatus 通过 gRPC 更新商户提现订单的状态。
func (c *WithdrawalClient) UpdateMerchantWithdrawStatus(ctx context.Context, req *taskv1.UpdateMerchantWithdrawRequest) error {
	logPrefix := "[WithdrawalClient.UpdateMerchantWithdrawStatus]"
	if ctx == nil {
		ctx = gctx.New()
	}
	if req == nil {
		return gerror.Newf("%s update request cannot be nil", logPrefix)
	}
	if req.WithdrawsId <= 0 {
		return gerror.Newf("%s withdraws_id must be positive", logPrefix)
	}

	callCtx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	g.Log().Debugf(callCtx, "%s Calling UpdateMerchantWithdraw with request: %+v", logPrefix, req)
	resp, err := c.client.UpdateMerchantWithdraw(callCtx, req)
	if err != nil {
		return gerror.Wrapf(err, "%s gRPC call UpdateMerchantWithdraw failed", logPrefix)
	}
	g.Log().Debugf(callCtx, "%s Received UpdateMerchantWithdraw response: %+v", logPrefix, resp)

	// 检查业务状态码
	if resp.Code != grpcSuccessCode {
		errMsg := fmt.Sprintf("%s UpdateMerchantWithdraw failed for withdraws_id %d with business code %d: %s",
			logPrefix, req.WithdrawsId, resp.Code, resp.Message)
		g.Log().Errorf(callCtx, errMsg)
		return gerror.New(errMsg)
	}

	// Check the oneof data_payload field for success confirmation
	if _, ok := resp.GetDataPayload().(*taskv1.ApiResponse_SuccessNoData); !ok {
		g.Log().Warningf(callCtx, "%s UpdateMerchantWithdraw response has success code %d but unexpected data_payload type. Received response: %+v", logPrefix, resp.Code, resp)
	}

	g.Log().Infof(callCtx, "%s Successfully updated status for merchant withdraws_id %d (State: %d)",
		logPrefix, req.WithdrawsId, req.State)
	return nil
}

// === Merchant Settlement Methods ===

// FetchPendingMerchantSettlements 从 gRPC 服务获取待处理的商户结算订单。
func (c *WithdrawalClient) FetchPendingMerchantSettlements(ctx context.Context, batchSize int) ([]*taskv1.MerchantSettlement, error) {
	logPrefix := "[WithdrawalClient.FetchPendingMerchantSettlements]"
	if ctx == nil {
		ctx = gctx.New()
	}
	if batchSize <= 0 {
		batchSize = 10 // Default batch size if invalid
	}

	req := &taskv1.ListMerchantSettlementsRequest{
		PageSize:               int32(batchSize),
		Page:                   1, // 获取第一页
		FilterState:            2, // 只获取处理中状态的结算 (2-处理中，已审核通过待自动处理)
		FilterSettlementsType:  0, // 不过滤结算类型
		FilterMerchantId:       0, // 不过滤商户ID
		FilterOrderNo:          "", // 不过滤订单号
	}

	callCtx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	g.Log().Debugf(callCtx, "%s Calling ListMerchantSettlements with request: %+v", logPrefix, req)
	resp, err := c.client.ListMerchantSettlements(callCtx, req)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s gRPC call ListMerchantSettlements failed", logPrefix)
	}
	g.Log().Debugf(callCtx, "%s Received ListMerchantSettlements response: %+v", logPrefix, resp)

	// 检查业务状态码
	if resp.Code != grpcSuccessCode {
		return nil, gerror.Newf("%s ListMerchantSettlements failed with business code %d: %s", logPrefix, resp.Code, resp.Message)
	}

	// Check the oneof data_payload field
	listMerchantSettlementsData, ok := resp.GetDataPayload().(*taskv1.ApiResponse_ListMerchantSettlementsData)
	if !ok || listMerchantSettlementsData == nil || listMerchantSettlementsData.ListMerchantSettlementsData == nil {
		g.Log().Errorf(callCtx, "%s ListMerchantSettlements response data_payload is not ListMerchantSettlementsResponse or is nil. Received response: %+v", logPrefix, resp)
		return []*taskv1.MerchantSettlement{}, gerror.Newf("%s unexpected response data format from server", logPrefix)
	}

	// Extract the ListMerchantSettlementsResponse from the oneof field
	listResp := listMerchantSettlementsData.ListMerchantSettlementsData

	// Check if the extracted list is nil
	if listResp.MerchantSettlements == nil {
		g.Log().Warningf(callCtx, "%s ListMerchantSettlements response contains nil merchant settlements list, returning empty list", logPrefix)
		return []*taskv1.MerchantSettlement{}, nil
	}

	g.Log().Infof(callCtx, "%s Successfully fetched %d pending merchant settlements (Total: %d)", logPrefix, len(listResp.MerchantSettlements), listResp.TotalCount)
	return listResp.MerchantSettlements, nil
}

// UpdateMerchantSettlementStatus 通过 gRPC 更新商户结算订单的状态。
func (c *WithdrawalClient) UpdateMerchantSettlementStatus(ctx context.Context, req *taskv1.UpdateMerchantSettlementRequest) error {
	logPrefix := "[WithdrawalClient.UpdateMerchantSettlementStatus]"
	if ctx == nil {
		ctx = gctx.New()
	}
	if req == nil {
		return gerror.Newf("%s update request cannot be nil", logPrefix)
	}
	if req.SettlementsId <= 0 {
		return gerror.Newf("%s settlements_id must be positive", logPrefix)
	}

	callCtx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	g.Log().Debugf(callCtx, "%s Calling UpdateMerchantSettlement with request: %+v", logPrefix, req)
	resp, err := c.client.UpdateMerchantSettlement(callCtx, req)
	if err != nil {
		return gerror.Wrapf(err, "%s gRPC call UpdateMerchantSettlement failed", logPrefix)
	}
	g.Log().Debugf(callCtx, "%s Received UpdateMerchantSettlement response: %+v", logPrefix, resp)

	// 检查业务状态码
	if resp.Code != grpcSuccessCode {
		errMsg := fmt.Sprintf("%s UpdateMerchantSettlement failed for settlements_id %d with business code %d: %s",
			logPrefix, req.SettlementsId, resp.Code, resp.Message)
		g.Log().Errorf(callCtx, errMsg)
		return gerror.New(errMsg)
	}

	// Check the oneof data_payload field for success confirmation
	if _, ok := resp.GetDataPayload().(*taskv1.ApiResponse_SuccessNoData); !ok {
		g.Log().Warningf(callCtx, "%s UpdateMerchantSettlement response has success code %d but unexpected data_payload type. Received response: %+v", logPrefix, resp.Code, resp)
	}

	g.Log().Infof(callCtx, "%s Successfully updated status for merchant settlements_id %d (State: %d)",
		logPrefix, req.SettlementsId, req.State)
	return nil
}
