package model

import (
	"errors"
	taskv1 "task-withdraw/api"
	"time"
)

// Transaction processing errors
var (
	ErrInvalidTransactionType = errors.New("invalid transaction type")
	ErrMissingTransactionData = errors.New("missing transaction data for specified type")
)

// TransactionType represents the type of transaction being processed
type TransactionType string

const (
	// TransactionTypeUserWithdrawal represents a user withdrawal transaction
	TransactionTypeUserWithdrawal TransactionType = "user_withdrawal"

	// TransactionTypeMerchantWithdrawal represents a merchant withdrawal transaction
	TransactionTypeMerchantWithdrawal TransactionType = "merchant_withdrawal"

	// TransactionTypeMerchantSettlement represents a merchant settlement transaction
	TransactionTypeMerchantSettlement TransactionType = "merchant_settlement"
)

// TransactionRequest represents a unified transaction request that can handle
// user withdrawals, merchant withdrawals, and merchant settlements
type TransactionRequest struct {
	// Type indicates which type of transaction this is
	Type TransactionType `json:"type"`

	// Union field - only one will be populated based on Type
	UserWithdrawal     *taskv1.Withdrawal         `json:"user_withdrawal,omitempty"`
	MerchantWithdrawal *taskv1.MerchantWithdraw   `json:"merchant_withdrawal,omitempty"`
	MerchantSettlement *taskv1.MerchantSettlement `json:"merchant_settlement,omitempty"`

	// Common metadata
	EnqueuedAt time.Time `json:"enqueued_at"`
	Retries    int       `json:"retries"`
}

// TransactionIdentifier provides a unified way to identify transactions across different types
type TransactionIdentifier struct {
	Type TransactionType `json:"type"`
	ID   uint64          `json:"id"`
}

// GetTransactionID returns the transaction ID based on the transaction type
func (tr *TransactionRequest) GetTransactionID() uint64 {
	switch tr.Type {
	case TransactionTypeUserWithdrawal:
		if tr.UserWithdrawal != nil {
			return uint64(tr.UserWithdrawal.UserWithdrawsId)
		}
	case TransactionTypeMerchantWithdrawal:
		if tr.MerchantWithdrawal != nil {
			return uint64(tr.MerchantWithdrawal.WithdrawsId)
		}
	case TransactionTypeMerchantSettlement:
		if tr.MerchantSettlement != nil {
			return uint64(tr.MerchantSettlement.SettlementsId)
		}
	}
	return 0
}

// GetOrderNo returns the order number based on the transaction type
func (tr *TransactionRequest) GetOrderNo() string {
	switch tr.Type {
	case TransactionTypeUserWithdrawal:
		if tr.UserWithdrawal != nil {
			return tr.UserWithdrawal.OrderNo
		}
	case TransactionTypeMerchantWithdrawal:
		if tr.MerchantWithdrawal != nil {
			return tr.MerchantWithdrawal.OrderNo
		}
	case TransactionTypeMerchantSettlement:
		if tr.MerchantSettlement != nil {
			return tr.MerchantSettlement.OrderNo
		}
	}
	return ""
}

// GetAmount returns the transaction amount based on the transaction type
func (tr *TransactionRequest) GetAmount() float64 {
	switch tr.Type {
	case TransactionTypeUserWithdrawal:
		if tr.UserWithdrawal != nil {
			return tr.UserWithdrawal.Amount
		}
	case TransactionTypeMerchantWithdrawal:
		if tr.MerchantWithdrawal != nil {
			return tr.MerchantWithdrawal.Amount
		}
	case TransactionTypeMerchantSettlement:
		if tr.MerchantSettlement != nil {
			return tr.MerchantSettlement.Amount
		}
	}
	return 0
}

// GetAddress returns the recipient address based on the transaction type
func (tr *TransactionRequest) GetAddress() string {
	switch tr.Type {
	case TransactionTypeUserWithdrawal:
		if tr.UserWithdrawal != nil {
			return tr.UserWithdrawal.Address
		}
	case TransactionTypeMerchantWithdrawal:
		if tr.MerchantWithdrawal != nil {
			return tr.MerchantWithdrawal.Address
		}
	case TransactionTypeMerchantSettlement:
		if tr.MerchantSettlement != nil {
			return tr.MerchantSettlement.Address
		}
	}
	return ""
}

// GetTokenName returns the token name based on the transaction type
func (tr *TransactionRequest) GetTokenName() string {
	switch tr.Type {
	case TransactionTypeUserWithdrawal:
		if tr.UserWithdrawal != nil {
			return tr.UserWithdrawal.Name
		}
	case TransactionTypeMerchantWithdrawal:
		if tr.MerchantWithdrawal != nil {
			return tr.MerchantWithdrawal.Name
		}
	case TransactionTypeMerchantSettlement:
		if tr.MerchantSettlement != nil {
			return tr.MerchantSettlement.Name
		}
	}
	return ""
}

// GetRetries returns the current retry count from the transaction data
func (tr *TransactionRequest) GetRetries() int32 {
	switch tr.Type {
	case TransactionTypeUserWithdrawal:
		if tr.UserWithdrawal != nil {
			return tr.UserWithdrawal.Retries
		}
	case TransactionTypeMerchantWithdrawal:
		if tr.MerchantWithdrawal != nil {
			return tr.MerchantWithdrawal.Retries
		}
	case TransactionTypeMerchantSettlement:
		if tr.MerchantSettlement != nil {
			return tr.MerchantSettlement.Retries
		}
	}
	return 0
}

// Validate checks if the transaction request is valid
func (tr *TransactionRequest) Validate() error {
	if tr.Type == "" {
		return ErrInvalidTransactionType
	}

	switch tr.Type {
	case TransactionTypeUserWithdrawal:
		if tr.UserWithdrawal == nil {
			return ErrMissingTransactionData
		}
	case TransactionTypeMerchantWithdrawal:
		if tr.MerchantWithdrawal == nil {
			return ErrMissingTransactionData
		}
	case TransactionTypeMerchantSettlement:
		if tr.MerchantSettlement == nil {
			return ErrMissingTransactionData
		}
	default:
		return ErrInvalidTransactionType
	}

	return nil
}

// NewUserWithdrawalRequest creates a new transaction request for user withdrawal
func NewUserWithdrawalRequest(withdrawal *taskv1.Withdrawal) *TransactionRequest {
	return &TransactionRequest{
		Type:           TransactionTypeUserWithdrawal,
		UserWithdrawal: withdrawal,
		EnqueuedAt:     time.Now(),
		Retries:        int(withdrawal.Retries),
	}
}

// NewMerchantWithdrawalRequest creates a new transaction request for merchant withdrawal
func NewMerchantWithdrawalRequest(withdrawal *taskv1.MerchantWithdraw) *TransactionRequest {
	return &TransactionRequest{
		Type:               TransactionTypeMerchantWithdrawal,
		MerchantWithdrawal: withdrawal,
		EnqueuedAt:         time.Now(),
		Retries:            int(withdrawal.Retries),
	}
}

// NewMerchantSettlementRequest creates a new transaction request for merchant settlement
func NewMerchantSettlementRequest(settlement *taskv1.MerchantSettlement) *TransactionRequest {
	return &TransactionRequest{
		Type:               TransactionTypeMerchantSettlement,
		MerchantSettlement: settlement,
		EnqueuedAt:         time.Now(),
		Retries:            int(settlement.Retries),
	}
}
