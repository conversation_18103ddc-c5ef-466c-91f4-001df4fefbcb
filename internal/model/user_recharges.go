package model

import ()

// UserRechargeCreateInput represents the input for creating a user recharge record.
// This structure encapsulates the data required to create a new entry in the user_recharges table.
type UserRechargeCreateInput struct {
	UserId               uint64 `json:"userId"`                         // User ID associated with the recharge
	TokenId              uint   `json:"tokenId"`                        // ID of the token being recharged
	Name                 string `json:"name"`                           // Token symbol, e.g., "ETH", "USDT"
	Chan                 string `json:"chan"`                           // Blockchain network name, e.g., "ETH", "TRX"
	TokenContractAddress string `json:"tokenContractAddress,omitempty"` // Optional: Contract address for non-native tokens
	FromAddress          string `json:"fromAddress"`                    // Sender address of the transaction
	ToAddress            string `json:"toAddress"`                      // Recipient address (user's deposit address)
	TxHash               string `json:"txHash"`                         // Transaction hash
	Amount               string `json:"amount"`                         // Recharge amount as a string for precision (use decimal library for calculations)
	Confirmations        uint64 `json:"confirmations"`                  // Number of block confirmations
}

// UserRechargeCreateOutput represents the output after successfully creating a user recharge record.
type UserRechargeCreateOutput struct {
	UserRechargesId uint `json:"userRechargesId"` // Primary key ID of the newly created user_recharges record
}
