package model

// LocalWalletSummary defines a local structure to hold relevant wallet summary data,
// avoiding direct dependency on the SDK's internal types.
type LocalWalletSummary struct {
	AvailableFunds map[string]int64 `json:"availableFunds"`
	// Add other fields here if needed in the future, e.g., HoldFunds
}

// NewLocalWalletSummary creates a new LocalWalletSummary instance.
func NewLocalWalletSummary() *LocalWalletSummary {
	return &LocalWalletSummary{
		AvailableFunds: make(map[string]int64),
		// Initialize other fields if added
	}
}
