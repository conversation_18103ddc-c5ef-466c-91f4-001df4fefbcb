package ports

import (
	"context"
	"task-withdraw/internal/model" // Assuming model types are still needed here

	"github.com/gogf/gf/v2/database/gdb"
)

// IUserRecharges defines the interface for user recharge operations.
// Moved from internal/service/user_recharges.go to break import cycle
type IUserRecharges interface {
	// CreateUserRecharge creates a new user recharge record.
	CreateUserRecharge(ctx context.Context, in *model.UserRechargeCreateInput) (*model.UserRechargeCreateOutput, error)
	// CheckTxHashExists checks if a transaction hash already exists in the user_recharges table.
	CheckTxHashExists(ctx context.Context, txHash string) (bool, error)
	// ProcessNewDeposit handles a newly found deposit: checks existence and creates a record if new.
	// It now uses the DepositInfo structure from the ports package.
	// Returns the output containing the new ID (if created), a boolean indicating if a new record was created, and an error.
	ProcessNewDeposit(ctx context.Context, deposit *DepositInfo) (output *model.UserRechargeCreateOutput, created bool, err error)

	// UpdateRechargeStatus updates the status of a user recharge record.
	UpdateRechargeStatus(ctx context.Context, tx gdb.TX, rechargeID uint64, state uint) error
}
