package cmd

import (
	"context"
	"errors"
	"fmt"
	_ "task-withdraw/internal/logic/redis"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/gtime"

	// Import the task logic package
	"task-withdraw/internal/task_registry" // Import the task registry
)

var (
	// RunTask defines the command for manually running a single task.
	RunTask = &gcmd.Command{
		Name: "run-task", // Explicitly set name
		// Usage field removed to allow GoFrame auto-generation
		Brief: "Manually run a registered task using the --name option", // Updated Brief
		Func:  runTaskFunc,
		// Options are handled within Func using parser.GetOpt()
		// No explicit Options field needed here in GoFrame v2 for this structure.
	}
)

// runTaskFunc is the execution logic for the run-task command.
func runTaskFunc(ctx context.Context, parser *gcmd.Parser) (err error) {
	g.Log("cmd.run-task").Debug(ctx, "Entering runTaskFunc...") // Added early logging

	err = gtime.SetTimeZone("Asia/Shanghai")
	if err != nil {
		panic(err)
	}

	logger := g.Log("cmd.run-task")            // Create a logger specific to this command
	taskName := parser.GetOpt("name").String() // Get the task name from the --name option

	if taskName == "" {
		// This case should ideally be caught by IsRequired, but double-check
		logger.Error(ctx, "The --name option is required.")
		// The Required:true in Options should prevent this, but good to keep a check.
		return errors.New("the --name option is required")
	}

	logger.Infof(ctx, "Attempting to manually run task: %s", taskName)

	adapter := g.Cfg().GetAdapter()
	if fileAdapter, ok := adapter.(*gcfg.AdapterFile); ok {
		fileAdapter.SetPath("manifest/config")
		g.Log().Info(context.Background(), "Configuration path set to: manifest/config")
	} else {
		g.Log().Warning(context.Background(), "Configuration adapter is not *gcfg.AdapterFile, cannot set path.")
	}

	// Get all registered tasks
	// Note: Assumes task registration happens during application initialization (via init functions)
	registeredTasks := task_registry.GetRegisteredTasks()
	if len(registeredTasks) == 0 {
		logger.Warning(ctx, "No tasks seem to be registered. Ensure task modules are correctly imported and registered.")
		// Depending on the desired behavior, you might return an error here or proceed.
		// Let's proceed but log a warning.
	}

	var foundTask *task_registry.TaskInfo
	for i := range registeredTasks {
		task := registeredTasks[i] // Get a pointer to the element for modification if needed, or just copy
		if task.Name == taskName {
			foundTask = &task // Store the pointer to the found task
			break
		}
	}

	if foundTask != nil {
		logger.Infof(ctx, "Found task '%s'. Executing now...", taskName)
		// Execute the task function
		// It's crucial that the task function handles its own context cancellation, errors, and logging appropriately.
		// Consider adding panic recovery around the execution if tasks might panic.
		func() {
			defer func() {
				if r := recover(); r != nil {
					logger.Errorf(ctx, "Panic recovered during execution of task '%s': %v", taskName, r)
					// Optionally set the error to be returned
					err = fmt.Errorf("panic occurred in task %s: %v", taskName, r)
				}
			}()
			// Execute the task's function
			foundTask.Func(ctx)
		}()

		// Check if the execution resulted in an error (set by the panic recovery)
		if err != nil {
			logger.Errorf(ctx, "Task '%s' execution failed due to panic.", taskName)
			return err // Return the error from the panic
		}

		logger.Infof(ctx, "Task '%s' execution finished.", taskName)
		return nil // Success
	}

	// Task not found
	logger.Errorf(ctx, "Task with name '%s' not found in the registry.", taskName)
	// List available tasks for better debugging
	availableTasks := make([]string, 0, len(registeredTasks))
	for _, t := range registeredTasks {
		availableTasks = append(availableTasks, t.Name)
	}
	logger.Infof(ctx, "Available tasks: %v", availableTasks)

	return fmt.Errorf("task '%s' not found", taskName)
}
