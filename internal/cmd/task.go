package cmd

import (
	"context"
	"os"
	"os/signal"
	"sync" // Import sync package
	"syscall"
	"task-withdraw/internal/boot"
	_ "task-withdraw/internal/logic/redis"
	wp "task-withdraw/internal/logic/task/withdrawal_processor" // Import the withdrawal processor package

	// "task-withdraw/internal/service" // 移除旧 service 导入
	"task-withdraw/internal/logic/task"                     // Import task package for energy checker
	"task-withdraw/internal/logic/task/grpc_updater"        // Import the gRPC updater worker package
	"task-withdraw/internal/logic/task/merchant_handler"    // Import the merchant handler worker package
	"task-withdraw/internal/logic/task/withdrawal_consumer" // Import the withdrawal consumer worker package
	"task-withdraw/internal/task_registry"                  // 新增注册表导入

	// 显式导入 task 包
	"github.com/gogf/gf/v2/frame/g" // Import g for config access
	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/gcron"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
)

var (
	Task = gcmd.Command{
		Name:  "task",
		Usage: "task",
		Brief: "start task scheduler and gRPC status updater worker", // Updated brief
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			glog.Info(ctx, "任务调度器和后台 Workers 启动...") // Updated log message

			err = gtime.SetTimeZone("Asia/Shanghai")
			if err != nil {
				panic(err)
			}
			//初始化boot
			boot.Initialize(ctx)

			// Create a cancellable context for the worker
			workerCtx, cancelWorker := context.WithCancel(ctx)
			defer cancelWorker() // Ensure cancellation on function exit

			// Use a WaitGroup to wait for the worker goroutine to finish
			var wg sync.WaitGroup

			// Start the gRPC Status Updater Worker in a goroutine
			wg.Add(1)
			go func() {
				defer wg.Done() // Signal completion when the goroutine exits
				grpc_updater.StartGrpcStatusUpdater(workerCtx)
				glog.Info(workerCtx, "gRPC Status Updater Worker has stopped.")
			}()

			// Start the Energy Order Checker Worker in a goroutine
			wg.Add(1)
			go func() {
				defer wg.Done()
				task.StartEnergyOrderChecker(workerCtx)
				glog.Info(workerCtx, "Energy Order Checker Worker has stopped.")
			}()

			// Start the Merchant Status Worker in a goroutine
			wg.Add(1)
			go func() {
				defer wg.Done()
				merchant_handler.StartMerchantStatusWorker(workerCtx)
				glog.Info(workerCtx, "Merchant Status Worker has stopped.")
			}()

			// --- Start Withdrawal Consumer Worker ---
			var consumerCfg withdrawal_consumer.ConsumerConfig
			if err := g.Cfg().MustGet(ctx, "withdrawalConsumer").Scan(&consumerCfg); err != nil {
				glog.Errorf(ctx, "无法加载 Withdrawal Consumer 配置: %v", err)
				// Decide if we should exit if consumer config fails
				// return err
			} else if consumerCfg.Enabled {
				// Load Withdrawal Processor Config as well, as consumer needs it
				processorCfg, err := wp.LoadConfig(ctx)
				if err != nil {
					glog.Errorf(ctx, "无法加载 Withdrawal Processor 配置: %v. Withdrawal Consumer 将不会启动。", err)
					// Decide if we should exit if processor config fails for consumer
					// return err
				} else {
					wg.Add(1)
					go func() {
						defer wg.Done()
						// Pass both consumerCfg and processorCfg to StartConsumers
						withdrawal_consumer.StartConsumers(workerCtx, consumerCfg, processorCfg)
						glog.Info(workerCtx, "Withdrawal Consumer Worker(s) have stopped.")
					}()
				}
			}

			// --- Cron Scheduler Setup ---
			cron := gcron.New()

			// 调用新的注册表应用函数
			err = task_registry.ApplyRegistrations(ctx, cron)
			if err != nil {
				// ApplyRegistrations 内部已经记录了详细错误，这里只记录总体失败信息
				glog.Errorf(ctx, "应用任务注册时发生错误: %v", err)
				// 根据需要决定是否在注册失败时退出，这里选择退出
				// Cancel the worker context before returning
				cancelWorker()
				wg.Wait() // Wait for worker to stop
				return err
			}

			// 检查是否有任务被成功调度
			if cron.Size() == 0 {
				glog.Warning(ctx, "没有任务被成功调度，调度器将不会启动。")
				// 可以选择直接退出或继续执行（取决于是否需要保持进程运行）
				// Cancel the worker context before returning
				// cancelWorker()
				// wg.Wait() // Wait for worker to stop
				// return nil // 或者根据需要返回错误
				// Let's allow the worker to run even if no cron jobs are scheduled
				glog.Info(ctx, "No cron jobs scheduled, but gRPC updater worker will continue running.")
			} else {
				glog.Infof(ctx, "共有 %d 个任务被成功调度。", cron.Size())
				cron.Start() // 启动调度器 (非阻塞)
				glog.Info(ctx, "Cron 调度器已启动.")
			}

			glog.Info(ctx, "等待信号退出...")

			// --- Signal Handling ---
			sigChan := make(chan os.Signal, 1)
			// 监听 SIGINT (Ctrl+C) 和 SIGTERM 信号
			signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

			// 阻塞等待信号
			sig := <-sigChan
			glog.Infof(ctx, "接收到信号 %s，准备退出...", sig.String())

			// 1. Cancel the worker context to signal all workers to stop
			glog.Info(ctx, "正在停止后台 Workers...")

			// Stop the energy order checker
			task.StopEnergyOrderChecker(ctx)

			cancelWorker()

			// 2. Close the cron scheduler
			if cron.Size() > 0 {
				glog.Info(ctx, "正在关闭 Cron 调度器...")
				cron.Close() // 优雅关闭 cron
				glog.Info(ctx, "Cron 调度器已关闭.")
			}

			// 3. Wait for all worker goroutines to finish
			glog.Info(ctx, "等待 Worker 协程退出...")
			wg.Wait()
			glog.Info(ctx, "Worker 协程已退出.")

			glog.Info(ctx, "所有服务已优雅关闭.")
			return nil // 正常退出时返回 nil
		},
	}
)
