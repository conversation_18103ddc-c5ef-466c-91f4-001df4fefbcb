package transaction

import (
	"context"
	"fmt"
	"strings"

	taskv1 "task-withdraw/api"
	wp "task-withdraw/internal/logic/task/withdrawal_processor"
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/eth"
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/tron"

	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"
)

// TransactionProcessor handles blockchain transaction processing
type TransactionProcessor struct {
	Config     *wp.WithdrawalConfig
	Limiter    *wp.MemoryRateLimiter
	EthSender  *eth.EthSender
	TronSender *tron.TronSender
}

// NewTransactionProcessor creates a new transaction processor
func NewTransactionProcessor(
	config *wp.WithdrawalConfig,
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) *TransactionProcessor {
	return &TransactionProcessor{
		Config:     config,
		Limiter:    limiter,
		EthSender:  eth<PERSON><PERSON>,
		TronSender: tron<PERSON><PERSON>,
	}
}

// ProcessWithdrawal processes a withdrawal transaction on the blockchain
func (tp *TransactionProcessor) ProcessWithdrawal(ctx context.Context, withdrawal *taskv1.Withdrawal) (txHash string, finalState int32, errorMessage string) {
	logPrefix := fmt.Sprintf("[TransactionProcessor][ID:%d]", withdrawal.UserWithdrawsId)
	
	// Derive withdrawal information
	derivedInfo, err := tp.deriveWithdrawalInfo(ctx, withdrawal)
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to derive withdrawal info: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		return "", 5, errorMessage // Failed state
	}
	
	glog.Infof(ctx, "%s Processing %s transaction: Amount=%s, To=%s", 
		logPrefix, derivedInfo.Blockchain, derivedInfo.Amount.String(), withdrawal.Address)
	
	// Validate token configuration
	tokenConfig, exists := tp.Config.EnabledTokens[derivedInfo.TokenKey]
	if !exists || !tokenConfig {
		errorMessage = fmt.Sprintf("Token %s is not enabled", derivedInfo.TokenKey)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		return "", 5, errorMessage // Failed state
	}
	
	// Select blockchain sender
	var selectedSender wp.BlockchainSender
	switch derivedInfo.Blockchain {
	case "ETH":
		selectedSender = tp.EthSender
		glog.Infof(ctx, "%s Using ETH sender", logPrefix)
	case "TRON":
		selectedSender = tp.TronSender
		glog.Infof(ctx, "%s Using TRON sender", logPrefix)
	default:
		errorMessage = fmt.Sprintf("Unsupported blockchain: %s", derivedInfo.Blockchain)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		return "", 5, errorMessage // Failed state
	}
	
	// Get contract address if it's a token transaction
	contractAddress := ""
	if derivedInfo.IsToken {
		contractAddress = tp.Config.TokenContracts[derivedInfo.TokenKey]
		if contractAddress == "" {
			errorMessage = fmt.Sprintf("Contract address not configured for token %s", derivedInfo.TokenKey)
			glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
			return "", 5, errorMessage // Failed state
		}
	}
	
	// Check balance before sending
	balance, err := selectedSender.GetBalance(ctx, derivedInfo.TokenKey, derivedInfo.Decimals, contractAddress)
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to check balance: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		return "", 5, errorMessage // Failed state
	}
	
	glog.Infof(ctx, "%s Balance check: Available=%s, Required=%s", 
		logPrefix, balance.String(), derivedInfo.Amount.String())
	
	// Check if balance is sufficient
	if balance.LessThan(derivedInfo.Amount) {
		errorMessage = fmt.Sprintf("Insufficient balance: Available=%s, Required=%s", 
			balance.String(), derivedInfo.Amount.String())
		glog.Warningf(ctx, "%s %s", logPrefix, errorMessage)
		return "", 2, errorMessage // Processing state (will retry)
	}
	
	// Send the transaction using the BlockchainSender interface
	glog.Infof(ctx, "%s Sending transaction to %s", logPrefix, withdrawal.Address)
	
	// Determine token standard (for ETH: ERC20, for TRON: TRC20)
	tokenStandard := ""
	if derivedInfo.IsToken {
		if derivedInfo.Blockchain == "ETH" {
			tokenStandard = "ERC20"
		} else if derivedInfo.Blockchain == "TRON" {
			tokenStandard = "TRC20"
		}
	}
	
	// Call SendTransaction with the correct signature
	txHash, err = selectedSender.SendTransaction(
		ctx,
		withdrawal.Address,     // recipient address
		derivedInfo.Amount,     // amount as decimal
		derivedInfo.TokenKey,   // symbol (ETH, TRX, USDT_ERC20, etc.)
		tokenStandard,          // standard (ERC20, TRC20, or empty for native)
		contractAddress,        // contract address (empty for native tokens)
		derivedInfo.Decimals,   // precision/decimals
	)
	
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to send transaction: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		
		// Check if it's insufficient balance
		if strings.Contains(strings.ToLower(err.Error()), "insufficient") {
			return "", 2, errorMessage // Processing state (will retry)
		}
		return "", 5, errorMessage // Failed state
	}
	
	// Success!
	glog.Infof(ctx, "%s Transaction sent successfully! TxHash: %s", logPrefix, txHash)
	return txHash, 4, "" // Completed state
}

// deriveWithdrawalInfo derives necessary information from withdrawal
func (tp *TransactionProcessor) deriveWithdrawalInfo(ctx context.Context, withdrawal *taskv1.Withdrawal) (*DerivedInfo, error) {
	info := &DerivedInfo{}
	
	// Determine token and blockchain from Name and Chan
	tokenName := strings.ToUpper(withdrawal.Name)
	blockchain := strings.ToUpper(withdrawal.Chan)
	
	// Map blockchain names
	switch blockchain {
	case "ETHEREUM", "ETH":
		info.Blockchain = "ETH"
	case "TRON", "TRX":
		info.Blockchain = "TRON"
	default:
		return nil, fmt.Errorf("unsupported blockchain: %s", blockchain)
	}
	
	// Determine token key
	if tokenName == "USDT" {
		if info.Blockchain == "ETH" {
			info.TokenKey = "USDT_ERC20"
			info.IsToken = true
		} else if info.Blockchain == "TRON" {
			info.TokenKey = "USDT_TRC20"
			info.IsToken = true
		}
	} else if tokenName == "ETH" {
		info.TokenKey = "ETH"
		info.IsToken = false
	} else if tokenName == "TRX" {
		info.TokenKey = "TRX"
		info.IsToken = false
	} else {
		return nil, fmt.Errorf("unsupported token: %s", tokenName)
	}
	
	// Get token precision/decimals
	if precision, exists := tp.Config.TokenPrecisions[info.TokenKey]; exists {
		info.Decimals = precision
	} else {
		return nil, fmt.Errorf("token precision not configured for %s", info.TokenKey)
	}
	
	// Convert amount to decimal
	info.Amount = decimal.NewFromFloat(withdrawal.ActualAmount)
	
	glog.Infof(ctx, "[deriveWithdrawalInfo] Token: %s, Blockchain: %s, Amount: %s, Decimals: %d",
		info.TokenKey, info.Blockchain, info.Amount.String(), info.Decimals)
	
	return info, nil
}

// DerivedInfo contains derived withdrawal information
type DerivedInfo struct {
	TokenKey   string
	Blockchain string
	Amount     decimal.Decimal
	Decimals   int
	IsToken    bool
}

