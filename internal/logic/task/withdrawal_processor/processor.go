package withdrawal_processor

import (
	"context"
	"encoding/json" // Added for JSON marshalling
	"fmt"           // Added for logging format
	"time"

	taskv1 "task-withdraw/api" // Import generated protobuf types with alias
	"task-withdraw/internal/config"
	"task-withdraw/internal/service" // Import service package

	// Removed unused imports:
	// "task-withdraw/internal/logic/task/withdrawal_processor/sender/eth"
	// "task-withdraw/internal/logic/task/withdrawal_processor/sender/tron"
	// "github.com/gogf/gf/v2/database/gredis"
	// "github.com/gogf/gf/v2/os/gcache"
	// "github.com/gogf/gf/v2/os/gctx"

	"github.com/gogf/gf/v2/frame/g" // Added for g.Cfg()
	"github.com/gogf/gf/v2/os/glog"
)

// ProcessPendingWithdrawals fetches pending withdrawals via gRPC and enqueues them into a Redis list for processing.
func ProcessPendingWithdrawals(ctx context.Context) {
	logPrefix := "[WithdrawalFetcher]" // Updated log prefix to reflect new role
	glog.Infof(ctx, "%s Task started.", logPrefix)
	startTime := time.Now()

	// Check if auto withdrawal is enabled
	autoWithdrawalEnabled, err := config.GetBool(ctx, "auto_withdrawal_setting.state", false)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to get auto_withdrawal_setting.state: %v. Assuming disabled.", logPrefix, err)
		autoWithdrawalEnabled = false
	}
	
	if !autoWithdrawalEnabled {
		glog.Warningf(ctx, "%s Auto withdrawal is disabled (auto_withdrawal_setting.state=false). Skipping task execution.", logPrefix)
		return
	}

	// 1. Load Configuration (Keep relevant parts if needed, e.g., BatchSize, Enabled flag)
	cfg, err := LoadConfig(ctx) // Assuming LoadConfig still loads necessary fields like BatchSize, Enabled
	if err != nil {
		glog.Errorf(ctx, "%s Failed to load configuration: %v. Task cannot proceed.", logPrefix, err)
		return
	}
	if !cfg.Enabled { // Keep the enabled check for the fetcher itself
		glog.Infof(ctx, "%s Task is disabled in configuration. Exiting.", logPrefix)
		return
	}

	// 2. Get gRPC Client from Service Registry
	withdrawalClient := service.WithdrawalClient()

	// 3. Fetch Pending Withdrawals via gRPC
	// 由于API不支持同时查询多个状态，我们需要请求两次
	// 第一次：获取新的待处理订单 (processing_status=1)
	// 第二次：获取余额不足的订单 (processing_status=2)
	
	var allPendingWithdrawals []*taskv1.Withdrawal
	
	// 3.1 获取状态1的订单（新的待处理订单）
	pendingWithdrawals, err := withdrawalClient.FetchPendingWithdrawals(ctx, cfg.BatchSize)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to fetch pending withdrawals (status=1) via gRPC: %v", logPrefix, err)
		// 继续尝试获取状态2的订单
	} else {
		allPendingWithdrawals = append(allPendingWithdrawals, pendingWithdrawals...)
		glog.Infof(ctx, "%s Found %d pending withdrawals with status=1", logPrefix, len(pendingWithdrawals))
	}
	
	// 3.2 获取状态2的订单（余额不足待重试的订单）
	insufficientBalanceWithdrawals, err := withdrawalClient.FetchPendingWithdrawalsWithStatus(ctx, cfg.BatchSize, 2)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to fetch insufficient balance withdrawals (status=2) via gRPC: %v", logPrefix, err)
		// 继续处理，即使获取状态2的订单失败
	} else {
		allPendingWithdrawals = append(allPendingWithdrawals, insufficientBalanceWithdrawals...)
		glog.Infof(ctx, "%s Found %d withdrawals with insufficient balance (status=2)", logPrefix, len(insufficientBalanceWithdrawals))
	}

	if len(allPendingWithdrawals) == 0 {
		glog.Infof(ctx, "%s No pending withdrawals found via gRPC.", logPrefix)
		glog.Infof(ctx, "%s Task finished. Duration: %s", logPrefix, time.Since(startTime))
		return
	}

	glog.Infof(ctx, "%s Found total %d pending withdrawals via gRPC to enqueue.", logPrefix, len(allPendingWithdrawals))

	// 4. Enqueue fetched withdrawals to Redis queue
	// Define the config key for the consumer queue. Ensure this key exists in your config file later.
	const cfgConsumerQueueNameKey = "withdrawalConsumer.redisQueueName"
	// Read the queue name from config, provide a default value.
	processingQueueName := g.Cfg().MustGet(ctx, cfgConsumerQueueNameKey, "queue:withdrawal_processing").String()

	if processingQueueName == "" {
		glog.Errorf(ctx, "%s Redis queue name for processing ('%s') is not configured or empty. Cannot enqueue tasks.", logPrefix, cfgConsumerQueueNameKey)
		return // Stop if queue name is missing
	}

	redisClient := service.Redis().Client() // Get default Redis client
	enqueuedCount := 0
	failedEnqueueCount := 0

	for _, withdrawal := range allPendingWithdrawals {
		// Create a specific log prefix for each record being enqueued
		recordLogPrefix := fmt.Sprintf("%s[EnqueueWithdrawalID:%d, OrderNo:%s]", logPrefix, withdrawal.UserWithdrawsId, withdrawal.OrderNo)

		// Serialize withdrawal data to JSON
		withdrawalBytes, err := json.Marshal(withdrawal)
		if err != nil {
			glog.Errorf(ctx, "%s Failed to marshal withdrawal data to JSON: %v. Skipping enqueue.", recordLogPrefix, err)
			failedEnqueueCount++
			continue // Skip this record
		}

		// LPUSH the JSON string to the processing queue
		_, err = redisClient.LPush(ctx, processingQueueName, string(withdrawalBytes))
		if err != nil {
			glog.Errorf(ctx, "%s Failed to LPUSH withdrawal data to Redis queue '%s': %v.", recordLogPrefix, processingQueueName, err)
			failedEnqueueCount++
			// Consider if we should stop the whole task on Redis error, or just skip this record.
			// For now, let's skip and count failures.
			continue
		}

		glog.Debugf(ctx, "%s Successfully enqueued withdrawal to queue '%s'.", recordLogPrefix, processingQueueName)
		enqueuedCount++
	}

	// Final log summarizing the enqueue operation
	glog.Infof(ctx, "%s Task finished. Enqueued: %d, Failed to Enqueue: %d. Duration: %s",
		logPrefix, enqueuedCount, failedEnqueueCount, time.Since(startTime))
}

// Removed ProcessStatus type and constants as they are no longer used here.
// Removed comments about moved functions.
