package withdrawal_processor

import (
	"context"
	"encoding/json" // Import encoding/json
	"fmt"

	// "strings"
	// "time"

	// taskv1 "task-withdraw/api"                                           // Import generated protobuf types
	// "task-withdraw/internal/logic/task/withdrawal_processor/sender/eth"  // Import new eth package
	// "task-withdraw/internal/logic/task/withdrawal_processor/sender/tron" // Import new tron package
	"task-withdraw/internal/service" // Import service package for Redis

	// "github.com/gogf/gf/v2/database/gredis" // Removed unused import
	// "github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	// "github.com/shopspring/decimal" // No longer needed here
)

// ProcessStatus indicates the outcome of processing a single withdrawal.
// Moved from processor.go
type ProcessStatus int

const (
	ProcessStatusSuccess ProcessStatus = iota // 0: Success
	ProcessStatusFailed                       // 1: Failed (non-retryable or max retries reached)
	ProcessStatusSkipped                      // 2: Skipped (e.g., limit exceeded, balance low, retry scheduled)
)

// StatusUpdateMessage defines the structure for messages pushed to the Redis queue.
type StatusUpdateMessage struct {
	WithdrawalID           int64  `json:"withdrawal_id"`
	TargetState            int32  `json:"target_state"` // Use the enum type directly
	TxHash                 string `json:"tx_hash,omitempty"`
	ErrorMessage           string `json:"error_message,omitempty"`            // Store the full JSON error history string
	Retries                int32  `json:"retries,omitempty"`                  // Only relevant for PENDING status
	LastAttemptTime        string `json:"last_attempt_time,omitempty"`        // Added for worker logic
	AutoWithdrawalProgress *int32 `json:"auto_withdrawal_progress,omitempty"` // Optional: auto withdrawal progress
}

// PushStatusUpdateToRedis constructs the message and pushes it to the Redis queue.
// This function remains relevant for pushing status updates from the Consumer.
func PushStatusUpdateToRedis(ctx context.Context, withdrawalID int64, state int32, txHash, errorMessage string, retries int32) error {
	return PushStatusUpdateToRedisWithProgress(ctx, withdrawalID, state, txHash, errorMessage, retries, nil)
}

// PushStatusUpdateToRedisWithProgress constructs the message with auto_withdrawal_progress and pushes it to the Redis queue.
func PushStatusUpdateToRedisWithProgress(ctx context.Context, withdrawalID int64, state int32, txHash, errorMessage string, retries int32, autoWithdrawalProgress *int32) error {
	logPrefix := fmt.Sprintf("[pushStatusUpdateToRedis:WithdrawalID:%d]", withdrawalID)

	// Get queue name from config using the same key as the worker
	// Note: Ensure the config key constant is accessible here or passed down.
	// For simplicity, we'll redefine the key here, but ideally it should be shared.
	const cfgRedisQueueNameKey = "grpcUpdater.redisQueueName"                                          // Same key as in worker.go
	queueName := g.Cfg().MustGet(ctx, cfgRedisQueueNameKey, "queue:withdrawal_status_update").String() // Keep default for safety

	if queueName == "" {
		// Use the config key in the error message
		return gerror.Newf("Redis queue name ('%s') for status updates is not configured or empty", cfgRedisQueueNameKey)
	}

	msg := StatusUpdateMessage{
		WithdrawalID:           withdrawalID,
		TargetState:            state,
		TxHash:                 txHash,
		ErrorMessage:           errorMessage,
		Retries:                retries, // Include retries for PENDING status
		AutoWithdrawalProgress: autoWithdrawalProgress,
		// LastAttemptTime will be set by the worker
	}

	msgBytes, err := json.Marshal(msg)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to marshal status update message: %v", logPrefix, err)
		return gerror.Wrap(err, "failed to marshal status update message")
	}

	redisClient := service.Redis().Client() // Get default Redis client
	_, err = redisClient.LPush(ctx, queueName, string(msgBytes))
	if err != nil {
		glog.Errorf(ctx, "%s Failed to LPush status update message to Redis queue '%s': %v", logPrefix, queueName, err)
		return gerror.Wrapf(err, "failed to LPush status update to Redis queue '%s'", queueName)
	}

	glog.Infof(ctx, "%s Successfully pushed status update message to Redis queue '%s' for state %d", logPrefix, queueName, state)
	return nil
}
