package withdrawal_processor

import (
	"context"
	"fmt"
	"strings"

	// "time" // Removed unused import

	taskv1 "task-withdraw/api"                                           // Import generated protobuf types
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/eth"  // Import eth package
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/tron" // Import tron package

	// "task-withdraw/internal/service" // May not be needed directly here

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"
)

// Define the blockchainSender interface again for clarity within this file if needed,
// or ensure it's accessible if defined elsewhere (e.g., in processor.go or handler.go).
// For now, let's assume it's defined/accessible from handler.go or processor.go.
// type blockchainSender interface { ... }

// --- Helper functions extracted from processSingleWithdrawal ---

// TODO: Implement extracted functions here based on the plan.
// - deriveWithdrawalInfo
// DeriveWithdrawalInfo extracts and derives key information from the withdrawal request.
func DeriveWithdrawalInfo(withdrawal *taskv1.Withdrawal) (symbol, chainType, tokenStandard, tokenConfigKey string, err error) {
	// --- Derivations from withdrawal data ---
	symbol = withdrawal.Name // Assume Name is the symbol (e.g., "USDT")
	if symbol == "" {
		err = gerror.New("withdrawal Name (symbol) is empty")
		return // Return early on error
	}
	symbolUpper := strings.ToUpper(symbol)

	chain := withdrawal.Chan // Use generated field name Chan
	if chain == "" {
		err = gerror.New("withdrawal chan (chain) is empty")
		return // Return early on error
	}
	chainType = strings.ToUpper(chain)

	tokenStandard = "" // Initialize
	tokenStandardUpper := ""
	switch chainType {
	case "ETH":
		if symbolUpper == "ETH" {
			tokenStandard = "NATIVE"
			tokenStandardUpper = "NATIVE"
		} else {
			tokenStandard = "ERC20" // Assume non-native on ETH is ERC20
			tokenStandardUpper = "ERC20"
		}
	case "TRON":
		if symbolUpper == "TRX" {
			tokenStandard = "NATIVE"
			tokenStandardUpper = "NATIVE"
		} else {
			tokenStandard = "TRC20" // Assume non-native on TRON is TRC20
			tokenStandardUpper = "TRC20"
		}
	default:
		err = gerror.Newf("cannot determine token standard for symbol '%s' on unsupported chain '%s'", symbol, chainType)
		return // Return early on error
	}

	tokenConfigKey = "" // Initialize
	if tokenStandardUpper == "NATIVE" {
		tokenConfigKey = symbolUpper
	} else {
		tokenConfigKey = fmt.Sprintf("%s_%s", symbolUpper, tokenStandardUpper)
	}
	return // Return named values
}

// CheckNergyState checks the NergyState for TRC20 withdrawals.
// Returns true for 'skip' if the withdrawal should be skipped due to NergyState.
func CheckNergyState(ctx context.Context, withdrawal *taskv1.Withdrawal, tokenStandardUpper string, recordLogPrefix string) (skip bool, err error) {
	return false, nil // Don't skip - proceed with withdrawal

	// if tokenStandardUpper == "TRC20" && withdrawal.Name == "USDT" {
	// 	// NergyState == 2 means energy is confirmed available
	// 	if withdrawal.NergyState != 2 { // Use CamelCase
	// 		glog.Infof(ctx, "%s Skipping TRC20 withdrawal because NergyState is %d (expected 2).", recordLogPrefix, withdrawal.NergyState)
	// 		return true, nil // Skip this withdrawal, no error
	// 	}
	// 	glog.Debugf(ctx, "%s NergyState check passed for TRC20 withdrawal (NergyState=%d).", recordLogPrefix, withdrawal.NergyState)
	// }
	// return false, nil
}

// GetTokenConfig retrieves the contract address and precision for a token from the configuration.
func GetTokenConfig(ctx context.Context, cfg *WithdrawalConfig, tokenConfigKey, tokenStandardUpper, recordLogPrefix string) (contractAddress string, precision int, err error) {
	var ok bool

	if tokenStandardUpper != "NATIVE" {
		contractAddress, ok = cfg.TokenContracts[tokenConfigKey]
		if !ok || contractAddress == "" {
			err = gerror.Newf("contract address for enabled token '%s' not found or empty in configuration (tokenContracts)", tokenConfigKey)
			glog.Errorf(ctx, "%s %v", recordLogPrefix, err)
			return "", 0, err // Return zero values and error
		}
		glog.Debugf(ctx, "%s Found contract address for %s from config: %s", recordLogPrefix, tokenConfigKey, contractAddress)
	} else {
		contractAddress = "" // Native tokens don't have contract address in this context
	}

	precision, ok = cfg.TokenPrecisions[tokenConfigKey]
	if !ok {
		err = gerror.Newf("chain precision not found in configuration for token key: %s", tokenConfigKey)
		glog.Errorf(ctx, "%s %v", recordLogPrefix, err)
		return contractAddress, 0, err // Return potentially found address, zero precision, and error
	}
	if precision <= 0 {
		err = gerror.Newf("invalid chain precision configured for token key %s: %d", tokenConfigKey, precision)
		glog.Errorf(ctx, "%s %v", recordLogPrefix, err)
		return contractAddress, 0, err // Return potentially found address, zero precision, and error
	}
	glog.Debugf(ctx, "%s Using precision %d from config for token key %s", recordLogPrefix, precision, tokenConfigKey)

	// Success
	return contractAddress, precision, nil
}

// CheckTokenEnabled checks if the withdrawal for the specific token is enabled in the configuration.
// Returns true for 'skip' if the token is disabled.
func CheckTokenEnabled(ctx context.Context, cfg *WithdrawalConfig, tokenConfigKey, symbolUpper, tokenStandardUpper, recordLogPrefix string) (skip bool, err error) {
	// Check if token withdrawal is enabled (Case-insensitive check)
	standardSymbolKeyUpper := tokenConfigKey // Use the derived config key

	isDisabled := false
	disabledByKey := ""

	// Priority 1: Check specific standard symbol key (e.g., USDT_ERC20)
	if tokenStandardUpper != "NATIVE" {
		if enabled, found := cfg.EnabledTokens[standardSymbolKeyUpper]; found && !enabled {
			isDisabled = true
			disabledByKey = standardSymbolKeyUpper
		}
	}

	// Priority 2: If not disabled by standard key, check base symbol key (e.g., ETH, TRX, USDT)
	if !isDisabled {
		if enabled, found := cfg.EnabledTokens[symbolUpper]; found && !enabled {
			isDisabled = true
			disabledByKey = symbolUpper
		}
	}

	if isDisabled {
		glog.Infof(ctx, "%s Withdrawal for token %s (checked key: %s) is disabled in configuration. Skipping.", recordLogPrefix, symbolUpper, disabledByKey) // Use symbolUpper for logging consistency
		return true, nil                                                                                                                                     // Skip this withdrawal, no error
	}

	// Log warning if keys were missing but we proceed
	if _, foundStd := cfg.EnabledTokens[standardSymbolKeyUpper]; !foundStd {
		if _, foundBase := cfg.EnabledTokens[symbolUpper]; !foundBase {
			glog.Warningf(ctx, "%s Neither standard key '%s' nor base key '%s' found in enabledTokens config for %s. Assuming enabled.", recordLogPrefix, standardSymbolKeyUpper, symbolUpper, symbolUpper) // Use symbolUpper
		}
	}

	// Token is enabled or assumed enabled
	return false, nil
}

// ValidateAddress performs basic format validation for the recipient address based on the chain type.
func ValidateAddress(ctx context.Context, recipientAddress string, chainType string, recordLogPrefix string) error {
	isValidAddress := false
	switch chainType {
	case "ETH":
		isValidAddress = len(recipientAddress) == 42 && strings.HasPrefix(recipientAddress, "0x")
	case "TRON":
		isValidAddress = len(recipientAddress) == 34 && strings.HasPrefix(recipientAddress, "T")
	default:
		// This case should ideally be caught earlier during derivation, but check defensively
		err := gerror.Newf("unsupported chain type %s for address validation", chainType)
		glog.Warningf(ctx, "%s %v", recordLogPrefix, err)
		return err // Return error for unsupported chain
	}

	if !isValidAddress {
		err := gerror.Newf("invalid recipient address format for %s: %s", chainType, recipientAddress)
		glog.Errorf(ctx, "%s %s", recordLogPrefix, err.Error())
		return err // Return error for invalid format
	}

	glog.Debugf(ctx, "%s Recipient address %s format validated for chain %s.", recordLogPrefix, recipientAddress, chainType)
	return nil // Address is valid
}

// ValidateAmountAndLimit converts the amount to decimal, checks if it's positive,
// and verifies it against the single withdrawal limit.
// Returns the amount as decimal.Decimal, true for 'skip' if the limit is exceeded.
func ValidateAmountAndLimit(ctx context.Context, withdrawal *taskv1.Withdrawal, limiter *MemoryRateLimiter, symbol string, recordLogPrefix string) (amountDecimal decimal.Decimal, skip bool, err error) {
	// Convert Amount to Decimal
	// Use ActualAmount from protobuf message (which is float64)
	amountDecimal = decimal.NewFromFloat(withdrawal.ActualAmount) // Use CamelCase. NewFromFloat returns 1 value.
	// decimal.NewFromFloat doesn't return error for standard floats.
	// Add checks for NaN or Inf if necessary, though protobuf float should prevent this.
	// if amountDecimal.IsNaN() || amountDecimal.IsInf() { ... }

	if amountDecimal.LessThanOrEqual(decimal.Zero) {
		err = gerror.Newf("withdrawal amount %s is not positive", amountDecimal.String())
		glog.Errorf(ctx, "%s %s", recordLogPrefix, err.Error())
		return amountDecimal, false, err // Return zero decimal, no skip, error
	}

	// Check Single Amount Limit
	allowed, errLimit := limiter.CheckSingleLimit(ctx, amountDecimal, symbol) // Use derived symbol
	if errLimit != nil {
		err = gerror.Wrap(errLimit, "failed to check single amount limit")
		glog.Errorf(ctx, "%s %v", recordLogPrefix, err)
		// Let the caller decide retry based on the error type via handleProcessingError
		return amountDecimal, false, err // Return amount, no skip, error
	}
	if !allowed {
		errMsg := fmt.Sprintf("Amount %s %s exceeds single limit. Skipping.", amountDecimal.String(), symbol)
		glog.Infof(ctx, "%s %s", recordLogPrefix, errMsg)
		return amountDecimal, true, nil // Return amount, skip=true, no error
	}
	glog.Debugf(ctx, "%s Amount %s %s is within single limit.", recordLogPrefix, amountDecimal.String(), symbol)

	// Amount is valid and within limit
	return amountDecimal, false, nil
}

// BlockchainSender defines the interface for interacting with different blockchain senders.
// This ensures that both ETH and TRON senders can be used interchangeably where this interface is expected.
type BlockchainSender interface {
	GetBalance(ctx context.Context, symbol string, precision int, contractAddress string) (decimal.Decimal, error)
	// SendTransaction now takes derived info instead of model.Tokens
	SendTransaction(ctx context.Context, recipient string, amount decimal.Decimal, symbol string, standard string, contractAddress string, precision int) (string, error)
	// Close() method is typically handled by the concrete type's defer call in processor.go
}

// SelectSenderAndCheckBalance selects the appropriate blockchain sender based on chain type,
// checks if the sender is available, and verifies if the hot wallet balance is sufficient.
// Returns the selected sender.
func SelectSenderAndCheckBalance(
	ctx context.Context,
	chainType string,
	ethSender *eth.EthSender, // Pass concrete senders
	tronSender *tron.TronSender, // Pass concrete senders
	symbol string,
	precision int,
	contractAddress string,
	tokenStandard string, // Add tokenStandard parameter
	amountDecimal decimal.Decimal, // Amount needed for withdrawal
	recordLogPrefix string,
) (selectedSender BlockchainSender, err error) { // Return the interface type

	senderAvailable := false

	switch chainType {
	case "ETH":
		if ethSender == nil {
			senderAvailable = false
		} else {
			senderAvailable = true
			selectedSender = ethSender // Assign concrete type to interface
		}
	case "TRON":
		if tronSender == nil {
			senderAvailable = false
		} else {
			senderAvailable = true
			selectedSender = tronSender // Assign concrete type to interface
		}
	default:
		// Should not happen if derivation is correct
		err = gerror.Newf("unsupported chain type %s for sender selection", chainType)
		glog.Errorf(ctx, "%s %v", recordLogPrefix, err)
		return nil, err // Non-retryable
	}

	if !senderAvailable {
		err = gerror.Newf("sender for chain %s not available or failed to initialize", chainType)
		glog.Warningf(ctx, "%s %s", recordLogPrefix, err.Error())
		// Let the caller decide retry based on the error type via handleProcessingError
		return nil, err
	}

	// Get balance using the selected sender
	balance, balanceErr := selectedSender.GetBalance(ctx, symbol, precision, contractAddress)
	// Let's refine: return nil on balance error, as we can't proceed.
	if balanceErr != nil {
		err = gerror.Wrapf(balanceErr, "failed to get hot wallet balance for %s", symbol)
		glog.Errorf(ctx, "%s %v", recordLogPrefix, err)
		return nil, err // Return nil sender and the wrapped error
	}

	// TRON-specific balance validation with 100 TRX reserve
	if chainType == "TRON" {
		return validateTronBalance(ctx, selectedSender, symbol, tokenStandard, contractAddress, precision, amountDecimal, recordLogPrefix)
	}

	// For other chains (ETH), simple balance check
	if balance.LessThan(amountDecimal) {
		err = gerror.Newf("insufficient hot wallet balance for %s: Have %s, Need %s",
			symbol, balance.String(), amountDecimal.String())
		glog.Errorf(ctx, "%s %s", recordLogPrefix, err.Error())
		return selectedSender, err
	}
	glog.Debugf(ctx, "%s Hot wallet balance sufficient for %s (Have: %s, Need: %s).", recordLogPrefix, symbol, balance.String(), amountDecimal.String())

	// Balance is sufficient, return the selected sender and no error
	return selectedSender, nil
}

// SendTransaction attempts to send the withdrawal transaction using the selected sender.
func SendTransaction(
	ctx context.Context,
	selectedSender BlockchainSender, // Use the interface
	recipientAddress string,
	amountDecimal decimal.Decimal,
	symbol string,
	tokenStandard string, // e.g., "NATIVE", "ERC20", "TRC20"
	contractAddress string,
	precision int,
	recordLogPrefix string,
) (txHash string, err error) {

	glog.Infof(ctx, "%s Attempting to send transaction via %T sender...", recordLogPrefix, selectedSender) // Log the concrete type if possible

	// Call SendTransaction with derived info
	txHash, err = selectedSender.SendTransaction(ctx, recipientAddress, amountDecimal, symbol, tokenStandard, contractAddress, precision)

	if err != nil {
		// Wrap the error for context, but the original error is likely more specific
		err = gerror.Wrap(err, "transaction sending failed")
		glog.Errorf(ctx, "%s %v", recordLogPrefix, err)
		return "", err // Return empty txHash and the error
	}

	glog.Infof(ctx, "%s Transaction sent successfully. TxHash: %s", recordLogPrefix, txHash)
	return txHash, nil // Return txHash and nil error
}

// validateTronBalance validates TRON balance with specific rules:
// - For TRX transfers: balance must be >= amount + 100 TRX
// - For TRC20 transfers: token balance must be >= amount AND TRX balance must be >= 100 TRX
func validateTronBalance(
	ctx context.Context,
	sender BlockchainSender,
	symbol string,
	tokenStandard string,
	contractAddress string,
	precision int,
	amountDecimal decimal.Decimal,
	recordLogPrefix string,
) (BlockchainSender, error) {
	reserveTRX, _ := decimal.NewFromString("100") // 100 TRX reserve

	// Check token balance (TRX or TRC20)
	tokenBalance, err := sender.GetBalance(ctx, symbol, precision, contractAddress)
	if err != nil {
		err = gerror.Wrapf(err, "failed to get %s balance", symbol)
		glog.Errorf(ctx, "%s %v", recordLogPrefix, err)
		return nil, err
	}

	if strings.ToUpper(tokenStandard) == "TRC20" {
		// TRC20 transfer: check both token and TRX balances
		
		// 1. Check TRC20 token balance
		if tokenBalance.LessThan(amountDecimal) {
			err = gerror.Newf("insufficient %s balance: Have %s, Need %s", 
				symbol, tokenBalance.String(), amountDecimal.String())
			glog.Errorf(ctx, "%s %s", recordLogPrefix, err.Error())
			return sender, err
		}
		
		// 2. Check TRX balance for fees (must have at least 100 TRX)
		trxBalance, trxErr := sender.GetBalance(ctx, "TRX", 6, "") // TRX has 6 decimals, no contract
		if trxErr != nil {
			err = gerror.Wrapf(trxErr, "failed to get TRX balance for fee payment")
			glog.Errorf(ctx, "%s %v", recordLogPrefix, err)
			return nil, err
		}
		
		if trxBalance.LessThan(reserveTRX) {
			err = gerror.Newf("insufficient TRX balance for fees: Have %s TRX, Need at least %s TRX", 
				trxBalance.String(), reserveTRX.String())
			glog.Errorf(ctx, "%s %s", recordLogPrefix, err.Error())
			return sender, err
		}
		
		glog.Debugf(ctx, "%s TRC20 balance validation passed: %s=%s, TRX=%s (reserve=%s)", 
			recordLogPrefix, symbol, tokenBalance.String(), trxBalance.String(), reserveTRX.String())
		
	} else {
		// Native TRX transfer: balance must be >= amount + 100 TRX reserve
		requiredTRX := amountDecimal.Add(reserveTRX)
		
		if tokenBalance.LessThan(requiredTRX) {
			err = gerror.Newf("insufficient TRX balance: Have %s TRX, Need %s TRX (Amount: %s + Reserve: %s)", 
				tokenBalance.String(), requiredTRX.String(), amountDecimal.String(), reserveTRX.String())
			glog.Errorf(ctx, "%s %s", recordLogPrefix, err.Error())
			return sender, err
		}
		
		glog.Debugf(ctx, "%s TRX balance validation passed: Have %s TRX, Need %s TRX (Amount: %s + Reserve: %s)", 
			recordLogPrefix, tokenBalance.String(), requiredTRX.String(), amountDecimal.String(), reserveTRX.String())
	}

	return sender, nil
}

// CheckAndPurchaseEnergyForTRC20 checks energy requirements for TRC20 transfers and purchases if needed
func CheckAndPurchaseEnergyForTRC20(
	ctx context.Context,
	withdrawal *taskv1.Withdrawal,
	tokenStandard string,
	poolAddress string, // 资金池地址
	recordLogPrefix string,
) error {
	// 只有TRC20代币才需要能量检查
	if strings.ToUpper(tokenStandard) != "TRC20" {
		return nil
	}

	logPrefix := recordLogPrefix + "[EnergyCheck]"
	glog.Infof(ctx, "%s starting energy check for TRC20 transfer to %s", logPrefix, withdrawal.Address)
	
	// TEMPORARY: Bypass energy check due to RPC connection issues
	// TODO: Remove this bypass once TRON RPC connection is stable
	bypassEnergyCheck := g.Cfg().MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.bypassCheck", false).Bool()
	if bypassEnergyCheck {
		glog.Warningf(ctx, "%s BYPASSING energy check as configured (bypassCheck=true)", logPrefix)
		return nil
	}

	// 创建能量管理器
	energyConfig := GetDefaultEnergyManagerConfig()
	energyManager := NewEnergyManager(energyConfig)

	// 从配置中获取iTRX API参数
	// 使用GF框架的配置系统直接从YAML读取
	var apiKey, apiSecret, apiBaseUrl string
	
	// 从YAML配置获取iTRX API参数
	apiKey = g.Cfg().MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.itrx.apiKey").String()
	apiSecret = g.Cfg().MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.itrx.apiSecret").String()
	apiBaseUrl = g.Cfg().MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.itrx.apiBaseUrl").String()
	
	if apiKey == "" || apiSecret == "" || apiBaseUrl == "" {
		return gerror.Newf("%s iTRX API configuration incomplete", logPrefix)
	}

	// 1. 首先处理任何待验证的能量订单
	shouldContinue, err := energyManager.ProcessPendingEnergyOrder(ctx, withdrawal.Address, apiKey, apiBaseUrl)
	if err != nil {
		return gerror.Wrapf(err, "%s failed to process pending energy order", logPrefix)
	}

	if !shouldContinue {
		// 有待验证的订单，需要等待
		return gerror.Newf("%s waiting for energy order completion", logPrefix)
	}

	// 2. 检查并购买能量（如果需要）
	energyOrder, err := energyManager.PurchaseEnergyIfNeeded(ctx, 
		poolAddress, 
		withdrawal.Address, 
		withdrawal.UserWithdrawsId, 
		apiKey, 
		apiSecret, 
		apiBaseUrl)
	if err != nil {
		return gerror.Wrapf(err, "%s failed to purchase energy if needed", logPrefix)
	}

	if energyOrder != nil {
		// 购买了能量，需要等待到账
		glog.Infof(ctx, "%s purchased energy order: %s, waiting for completion", logPrefix, energyOrder.OrderID)
		return gerror.Newf("%s energy purchase initiated, task will retry", logPrefix)
	}

	// 能量充足，可以继续转账
	glog.Infof(ctx, "%s energy check passed, ready for TRC20 transfer", logPrefix)
	return nil
}
