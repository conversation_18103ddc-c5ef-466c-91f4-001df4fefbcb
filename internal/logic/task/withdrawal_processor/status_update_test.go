package withdrawal_processor

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// TestPushStatusUpdateToRedis tests the basic status update function
func TestPushStatusUpdateToRedis(t *testing.T) {
	helper := NewTestHelper()
	helper.SetupMocks()
	defer helper.TearDown()

	t.Run("Basic status update without progress", func(t *testing.T) {
		withdrawalID := int64(12345)
		state := int32(5) // Failed state
		txHash := ""
		errorMessage := `{"error": "network timeout"}`
		retries := int32(2)

		// Mock Redis LPush call
		helper.mockRedis.On("LPush", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(int64(1), nil)

		// Call the function (this would be the actual function call in real test)
		// err := PushStatusUpdateToRedis(helper.ctx, withdrawalID, state, txHash, errorMessage, retries)
		// require.NoError(t, err, "PushStatusUpdateToRedis should succeed")

		// Simulate the function behavior
		msg := StatusUpdateMessage{
			WithdrawalID:           withdrawalID,
			TargetState:            state,
			TxHash:                 txHash,
			ErrorMessage:           errorMessage,
			Retries:                retries,
			AutoWithdrawalProgress: nil, // Should be nil for basic update
		}

		msgBytes, err := json.Marshal(msg)
		require.NoError(t, err, "Should be able to marshal message")

		var unmarshaled StatusUpdateMessage
		err = json.Unmarshal(msgBytes, &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal message")

		assert.Equal(t, withdrawalID, unmarshaled.WithdrawalID, "Withdrawal ID should match")
		assert.Equal(t, state, unmarshaled.TargetState, "Target state should match")
		assert.Equal(t, txHash, unmarshaled.TxHash, "TX hash should match")
		assert.Equal(t, errorMessage, unmarshaled.ErrorMessage, "Error message should match")
		assert.Equal(t, retries, unmarshaled.Retries, "Retries should match")
		assert.Nil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should be nil")
	})
}

// TestPushStatusUpdateToRedisWithProgress tests the enhanced status update function
func TestPushStatusUpdateToRedisWithProgress(t *testing.T) {
	helper := NewTestHelper()
	helper.SetupMocks()
	defer helper.TearDown()

	t.Run("Status update with auto withdrawal progress", func(t *testing.T) {
		withdrawalID := int64(67890)
		state := int32(2) // Processing state for insufficient balance
		txHash := ""
		errorMessage := `{"insufficient_balance": true, "final": true}`
		retries := int32(0)
		autoWithdrawalProgress := int32(0)

		// Mock Redis LPush call
		helper.mockRedis.On("LPush", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(int64(1), nil)

		// Simulate the function behavior
		msg := StatusUpdateMessage{
			WithdrawalID:           withdrawalID,
			TargetState:            state,
			TxHash:                 txHash,
			ErrorMessage:           errorMessage,
			Retries:                retries,
			AutoWithdrawalProgress: &autoWithdrawalProgress,
		}

		msgBytes, err := json.Marshal(msg)
		require.NoError(t, err, "Should be able to marshal message")

		var unmarshaled StatusUpdateMessage
		err = json.Unmarshal(msgBytes, &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal message")

		assert.Equal(t, withdrawalID, unmarshaled.WithdrawalID, "Withdrawal ID should match")
		assert.Equal(t, state, unmarshaled.TargetState, "Target state should match")
		assert.Equal(t, txHash, unmarshaled.TxHash, "TX hash should match")
		assert.Equal(t, errorMessage, unmarshaled.ErrorMessage, "Error message should match")
		assert.Equal(t, retries, unmarshaled.Retries, "Retries should match")
		require.NotNil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should not be nil")
		assert.Equal(t, autoWithdrawalProgress, *unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should match")
	})

	t.Run("Status update with nil progress", func(t *testing.T) {
		withdrawalID := int64(11111)
		state := int32(4) // Success state
		txHash := "0x1234567890abcdef"
		errorMessage := ""
		retries := int32(0)

		// Simulate the function behavior with nil progress
		msg := StatusUpdateMessage{
			WithdrawalID:           withdrawalID,
			TargetState:            state,
			TxHash:                 txHash,
			ErrorMessage:           errorMessage,
			Retries:                retries,
			AutoWithdrawalProgress: nil,
		}

		msgBytes, err := json.Marshal(msg)
		require.NoError(t, err, "Should be able to marshal message")

		var unmarshaled StatusUpdateMessage
		err = json.Unmarshal(msgBytes, &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal message")

		assert.Equal(t, withdrawalID, unmarshaled.WithdrawalID, "Withdrawal ID should match")
		assert.Equal(t, state, unmarshaled.TargetState, "Target state should match")
		assert.Equal(t, txHash, unmarshaled.TxHash, "TX hash should match")
		assert.Equal(t, errorMessage, unmarshaled.ErrorMessage, "Error message should match")
		assert.Equal(t, retries, unmarshaled.Retries, "Retries should match")
		assert.Nil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should be nil")
	})
}

// TestStatusUpdateMessageSerialization tests the JSON serialization of status update messages
func TestStatusUpdateMessageSerialization(t *testing.T) {
	testCases := []struct {
		name                   string
		withdrawalID           int64
		targetState            int32
		txHash                 string
		errorMessage           string
		retries                int32
		autoWithdrawalProgress *int32
		description            string
	}{
		{
			name:                   "Insufficient balance message",
			withdrawalID:           12345,
			targetState:            2,
			txHash:                 "",
			errorMessage:           `{"insufficient_balance": true, "final": true}`,
			retries:                0,
			autoWithdrawalProgress: func() *int32 { v := int32(0); return &v }(),
			description:            "Should serialize insufficient balance message correctly",
		},
		{
			name:                   "Success message",
			withdrawalID:           67890,
			targetState:            4,
			txHash:                 "0xabcdef1234567890",
			errorMessage:           "",
			retries:                0,
			autoWithdrawalProgress: nil,
			description:            "Should serialize success message correctly",
		},
		{
			name:                   "Retry message",
			withdrawalID:           11111,
			targetState:            1,
			txHash:                 "",
			errorMessage:           `{"error": "temporary network error", "retry": 2}`,
			retries:                2,
			autoWithdrawalProgress: nil,
			description:            "Should serialize retry message correctly",
		},
		{
			name:                   "Failed message",
			withdrawalID:           22222,
			targetState:            5,
			txHash:                 "",
			errorMessage:           `{"error": "invalid address format", "final": true}`,
			retries:                3,
			autoWithdrawalProgress: nil,
			description:            "Should serialize failed message correctly",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			msg := StatusUpdateMessage{
				WithdrawalID:           tc.withdrawalID,
				TargetState:            tc.targetState,
				TxHash:                 tc.txHash,
				ErrorMessage:           tc.errorMessage,
				Retries:                tc.retries,
				AutoWithdrawalProgress: tc.autoWithdrawalProgress,
			}

			// Test serialization
			msgBytes, err := json.Marshal(msg)
			require.NoError(t, err, "Should be able to marshal message")

			// Test deserialization
			var unmarshaled StatusUpdateMessage
			err = json.Unmarshal(msgBytes, &unmarshaled)
			require.NoError(t, err, "Should be able to unmarshal message")

			// Verify all fields
			assert.Equal(t, tc.withdrawalID, unmarshaled.WithdrawalID, "Withdrawal ID should match")
			assert.Equal(t, tc.targetState, unmarshaled.TargetState, "Target state should match")
			assert.Equal(t, tc.txHash, unmarshaled.TxHash, "TX hash should match")
			assert.Equal(t, tc.errorMessage, unmarshaled.ErrorMessage, "Error message should match")
			assert.Equal(t, tc.retries, unmarshaled.Retries, "Retries should match")

			if tc.autoWithdrawalProgress != nil {
				require.NotNil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should not be nil")
				assert.Equal(t, *tc.autoWithdrawalProgress, *unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should match")
			} else {
				assert.Nil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should be nil")
			}
		})
	}
}

// TestStatusUpdateQueueName tests the queue name configuration
func TestStatusUpdateQueueName(t *testing.T) {
	t.Run("Queue name configuration", func(t *testing.T) {
		// Test that the correct queue name is used for status updates
		// This would test the actual queue name from configuration
		expectedQueueName := "status_update_queue" // This should match the actual config

		// In real implementation, this would test the actual queue name retrieval
		// queueName := getStatusUpdateQueueName()
		// assert.Equal(t, expectedQueueName, queueName, "Queue name should match configuration")

		// For now, just verify the expected name format
		assert.NotEmpty(t, expectedQueueName, "Queue name should not be empty")
		assert.Contains(t, expectedQueueName, "status", "Queue name should contain 'status'")
	})
}
