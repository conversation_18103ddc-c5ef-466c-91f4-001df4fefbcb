package tron

import (
	"context"
	"crypto/ecdsa"
	"strings"

	"task-withdraw/internal/utility/crypto"
	tronwallet "task-withdraw/internal/utility/tron-wallet" // Keep for compatibility

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/glog"
)

// --- Configuration Structs specific to tron package ---

// RpcConfigTron holds only the RPC fields needed by the tron sender.
type RpcConfigTron struct {
	Url    string
	ApiKey string // Changed from ApiKeyEnvVar
}

// WalletConfigTron holds only the Wallet fields needed by the tron sender.
type WalletConfigTron struct {
	PrivateKey       string
	PrivateKeyEnvVar string
	Address          string
}

// TxParamConfigTron holds only the TxParam fields needed by the tron sender.
type TxParamConfigTron struct {
	FeeLimitSun      int64
}

// --- TronSender Struct ---

// TronSender handles sending TRX and TRC20 token transactions.
type TronSender struct {
	client     crypto.BlockchainClient // Use unified crypto client
	privateKey *ecdsa.PrivateKey       // Private key
	publicKey  string                  // Base58 address
	txParams   TxParamConfigTron       // Store tron-specific tx params
}

// NewTronSender creates a new TronSender instance.
func NewTronSender(ctx context.Context, rpcCfg RpcConfigTron, walletCfg WalletConfigTron, txParamCfg TxParamConfigTron) (*TronSender, error) {
	logPrefix := "[NewTronSender]"

	// 1. Initialize crypto client
	// Determine if TLS should be used based on the endpoint
	// Nile testnet uses insecure connection, mainnet uses TLS
	useTLS := true
	if strings.Contains(strings.ToLower(rpcCfg.Url), "nile") {
		useTLS = false
	}
	
	tronConfig := &crypto.ClientConfig{
		ChainType: crypto.ChainTRON,
		RPCUrl:    rpcCfg.Url,
		APIKey:    rpcCfg.ApiKey,
		UseTLS:    useTLS,
	}

	client, err := crypto.NewTRONClient(tronConfig)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to create TRON client", logPrefix)
	}
	glog.Infof(ctx, "%s TRON crypto client connected to %s", logPrefix, rpcCfg.Url)

	// 2. Validate private key
	if walletCfg.PrivateKey == "" {
		return nil, gerror.Newf("%s 'privateKey' config field is empty for TRON wallet", logPrefix)
	}

	// Validate private key format using crypto client
	if !client.ValidatePrivateKey(walletCfg.PrivateKey) {
		return nil, gerror.Newf("%s invalid TRON private key format", logPrefix)
	}
	glog.Infof(ctx, "%s TRON private key validated successfully", logPrefix)

	// 3. Parse private key using tron-wallet for backward compatibility
	privateKey, err := tronwallet.PrivateKeyFromHex(walletCfg.PrivateKey)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to parse TRON private key", logPrefix)
	}

	// 4. Derive address using crypto client
	derivedAddress, err := client.GetAddressFromPrivateKey(walletCfg.PrivateKey)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to derive address from private key", logPrefix)
	}

	// Validate configured address if provided
	if walletCfg.Address != "" {
		if !client.ValidateAddress(walletCfg.Address) {
			glog.Warningf(ctx, "%s Configured TRON address '%s' is invalid. Using derived address '%s'.", logPrefix, walletCfg.Address, derivedAddress)
		} else if derivedAddress != walletCfg.Address {
			glog.Warningf(ctx, "%s Configured TRON address %s does not match derived address %s. Using derived address for safety.",
				logPrefix, walletCfg.Address, derivedAddress)
		} else {
			glog.Debugf(ctx, "%s Configured TRON address %s matches derived address.", logPrefix, walletCfg.Address)
		}
	}

	// 5. Validate transaction parameters
	if txParamCfg.FeeLimitSun <= 0 {
		return nil, gerror.Newf("%s invalid FeeLimitSun configuration: %d (must be positive)", logPrefix, txParamCfg.FeeLimitSun)
	}
	glog.Debugf(ctx, "%s Validated FeeLimitSun: %d Sun", logPrefix, txParamCfg.FeeLimitSun)

	glog.Infof(ctx, "%s TRON Sender initialized for address %s", logPrefix, derivedAddress)
	sender := &TronSender{
		client:     client,
		privateKey: privateKey,
		publicKey:  derivedAddress,
		txParams:   txParamCfg,
	}
	return sender, nil
}

// GetBalance method moved to tron_sender_balance.go

// SendTransaction method moved to tron_sender_tx.go

// Close handles any necessary cleanup.
func (s *TronSender) Close() {
	logPrefix := "[TronSender.Close]"
	if s.client != nil {
		glog.Debugf(context.Background(), "%s Closing TRON crypto client connection.", logPrefix)
		// Note: crypto client may not have explicit Close method
	} else {
		glog.Debugf(context.Background(), "%s TRON client was nil, nothing to close.", logPrefix)
	}
}
