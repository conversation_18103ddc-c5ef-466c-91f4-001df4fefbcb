package eth

import (
	"context"
	// "encoding/hex" // Removed hex encoding import
	"fmt"
	"math/big"
	"strings"

	// "task-withdraw/internal/service" // No longer needed here

	// "github.com/ethereum/go-ethereum/accounts/abi" // No longer needed directly here
	ethereum "github.com/ethereum/go-ethereum" // Import ethereum package for CallMsg
	"github.com/ethereum/go-ethereum/common"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"
)

// GetBalance retrieves the balance of the hot wallet for a given symbol (ETH or ERC20).
// For ERC20 tokens, contractAddressStr and precision must be provided.
func (s *EthSender) GetBalance(ctx context.Context, symbol string, precision int, contractAddressStr string) (decimal.Decimal, error) {
	logPrefix := fmt.Sprintf("[EthSender.GetBalance:%s]", symbol)
	glog.Debugf(ctx, "%s Getting balance for address %s", logPrefix, s.publicKey.Hex())

	// Check if it's native ETH balance request
	// contractAddressStr will be empty for native ETH, passed from withdrawal_handler
	if contractAddressStr == "" {
		if strings.ToUpper(symbol) != "ETH" {
			// This case should ideally not happen if called correctly from handler
			return decimal.Zero, gerror.Newf("%s contract address is empty but symbol is not ETH (%s)", logPrefix, symbol)
		}
		// Get ETH balance
		balanceWei, err := s.client.BalanceAt(ctx, s.publicKey, nil) // nil for latest block
		if err != nil {
			return decimal.Zero, gerror.Wrapf(err, "%s failed to get ETH balance for %s", logPrefix, s.publicKey.Hex())
		}
		// Convert Wei to Ether using decimal (ETH always has 18 decimals)
		balanceDecimal := decimal.NewFromBigInt(balanceWei, -18)
		glog.Debugf(ctx, "%s ETH Balance: %s ETH (Wei: %s)", logPrefix, balanceDecimal.String(), balanceWei.String())
		return balanceDecimal, nil
	}

	// --- Handle ERC20 Token Balance ---

	// Ensure balanceOf ABI is loaded
	if err := loadBalanceOfABI(); err != nil { // Use the specific loader
		return decimal.Zero, gerror.Wrap(err, "failed to load balanceOf ABI for balance check")
	}

	// Validate and convert contract address string
	if !common.IsHexAddress(contractAddressStr) {
		return decimal.Zero, gerror.Newf("%s invalid contract address format: %s", logPrefix, contractAddressStr)
	}
	contractAddress := common.HexToAddress(contractAddressStr)

	// Validate precision (passed from handler, now from config)
	if precision <= 0 {
		// Precision should always be positive, this indicates a config error.
		return decimal.Zero, gerror.Newf("%s invalid precision value %d for token %s from config", logPrefix, precision, symbol)
	}

	// Pack the balanceOf function call using the specific balanceOfABI
	callData, err := balanceOfABI.Pack("balanceOf", s.publicKey) // Use balanceOfABI
	if err != nil {
		return decimal.Zero, gerror.Wrapf(err, "%s failed to pack balanceOf call data for %s", logPrefix, symbol)
	}

	// Prepare the call message
	msg := ethereum.CallMsg{
		To:   &contractAddress, // Use the validated address from parameter
		Data: callData,
	}

	// Call the contract
	resultBytes, err := s.client.CallContract(ctx, msg, nil) // nil for latest block
	if err != nil {
		return decimal.Zero, gerror.Wrapf(err, "%s failed to call balanceOf for %s contract %s", logPrefix, symbol, contractAddress.Hex())
	}

	// Manually decode the resultBytes as uint256, bypassing UnpackIntoInterface
	var balanceInt big.Int
	if len(resultBytes) == 0 {
		// Handle empty result - likely means balance is 0 or contract doesn't exist/support balanceOf correctly
		glog.Warningf(ctx, "%s CallContract returned empty result for balanceOf %s contract %s. Assuming zero balance.", logPrefix, symbol, contractAddress.Hex())
		// balanceInt remains 0, proceed to convert to decimal
	} else if len(resultBytes) == 32 {
		// Expected length for uint256, decode directly
		balanceInt.SetBytes(resultBytes)
		// Removed diagnostic log for manual decoding
	} else {
		// Unexpected result length
		// Use fmt.Sprintf to include hex in the error message if needed, or remove hex part
		err = gerror.Newf("%s unexpected result length (%d bytes) from balanceOf call, expected 0 or 32", logPrefix, len(resultBytes))
		// Removed glog.Errorf, error is returned and handled by caller
		return decimal.Zero, err
	}

	// Convert smallest unit to decimal using token precision passed as parameter
	balanceDecimal := decimal.NewFromBigInt(&balanceInt, -int32(precision))
	// Removed diagnostic log for final balance
	return balanceDecimal, nil
}
