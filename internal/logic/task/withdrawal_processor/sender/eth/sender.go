package eth

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"math/big"
	"strings"
	"sync"

	// "task-withdraw/internal/model/entity" // Moved to balance/tx files
	// "task-withdraw/internal/service" // Removed as unused in this file

	// "github.com/ethereum/go-ethereum/accounts/abi" // Moved to eth_abi.go
	"github.com/ethereum/go-ethereum/common"
	// ethereum "github.com/ethereum/go-ethereum" // Moved to balance/tx files
	// "github.com/ethereum/go-ethereum/core/types" // Moved to eth_sender_tx.go
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/genv" // Import genv for environment variables
	"github.com/gogf/gf/v2/os/glog"

	// "github.com/shopspring/decimal" // Moved to balance/tx files
	"github.com/shopspring/decimal" // Need decimal for maxFeeEthDecimal
)

// --- Configuration Structs specific to eth package ---

// RpcConfigEth holds only the RPC fields needed by the eth sender.
type RpcConfigEth struct {
	Url     string
	ChainId int64
}

// WalletConfigEth holds only the Wallet fields needed by the eth sender.
type WalletConfigEth struct {
	PrivateKey       string
	PrivateKeyEnvVar string
	Address          string
}

// TxParamConfigEth holds only the TxParam fields needed by the eth sender.
type TxParamConfigEth struct {
	MaxFeeEth        string          // Keep as string for input flexibility
	maxFeeEthDecimal decimal.Decimal // Parsed value stored internally
}

// --- EthSender Struct ---

// EthSender handles sending ETH and ERC20 token transactions.
type EthSender struct {
	client      *ethclient.Client
	redisClient *gredis.Redis // For nonce management if configured
	privateKey  *ecdsa.PrivateKey
	publicKey   common.Address
	chainId     *big.Int
	nonceLock   sync.Mutex       // Mutex for nonce management if using RPC strategy
	txParams    TxParamConfigEth // Use the eth-specific config struct
	// Removed direct storage of rpcCfg and walletCfg as their needed values are used during init or stored elsewhere
}

// NewEthSender creates a new EthSender instance.
// It requires loading the private key securely.
func NewEthSender(ctx context.Context, rpcCfg RpcConfigEth, walletCfg WalletConfigEth, txParamCfg TxParamConfigEth, redis *gredis.Redis) (*EthSender, error) {
	logPrefix := "[NewEthSender]"
	var pkStr string
	var pkSource string
	var err error

	// 1. Try loading private key directly from config
	if walletCfg.PrivateKey != "" {
		pkStr = walletCfg.PrivateKey
		pkSource = "config field 'privateKey'"
		glog.Warningf(ctx, "%s Loading ETH private key directly from config field. Ensure this is not used in production.", logPrefix)
	} else if walletCfg.PrivateKeyEnvVar != "" {
		// 2. Fallback to environment variable
		pkStr = genv.Get(walletCfg.PrivateKeyEnvVar).String()
		pkSource = fmt.Sprintf("env var '%s'", walletCfg.PrivateKeyEnvVar)
		if pkStr == "" {
			return nil, gerror.Newf("%s ETH private key env var '%s' is set in config but the env var is empty", logPrefix, walletCfg.PrivateKeyEnvVar)
		}
	} else {
		// 3. Error if neither is provided
		return nil, gerror.Newf("%s Neither 'privateKey' config field nor 'privateKeyEnvVar' is configured for ETH wallet", logPrefix)
	}

	// 4. Parse the private key string (remove 0x prefix if present)
	if strings.HasPrefix(strings.ToLower(pkStr), "0x") {
		pkStr = pkStr[2:]
	}
	privateKey, err := crypto.HexToECDSA(pkStr)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to parse ETH private key from %s", logPrefix, pkSource)
	}
	glog.Infof(ctx, "%s Successfully loaded ETH private key from %s", logPrefix, pkSource)

	// 4.5 Parse MaxFeeEth from TxParamConfigEth
	if txParamCfg.MaxFeeEth == "" {
		return nil, gerror.Newf("%s txParams.ETH.maxFeeEth is required but not provided", logPrefix)
	}
	parsedMaxFee, err := decimal.NewFromString(txParamCfg.MaxFeeEth)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to parse txParams.ETH.maxFeeEth: %s", logPrefix, txParamCfg.MaxFeeEth)
	}
	if parsedMaxFee.LessThanOrEqual(decimal.Zero) {
		return nil, gerror.Newf("%s txParams.ETH.maxFeeEth must be positive, got: %s", logPrefix, txParamCfg.MaxFeeEth)
	}
	txParamCfg.maxFeeEthDecimal = parsedMaxFee // Store the parsed decimal

	// 5. Derive public key and address
	publicKeyECDSA, ok := privateKey.Public().(*ecdsa.PublicKey)
	if !ok {
		return nil, gerror.New("failed to derive public key from private key")
	}
	senderAddress := crypto.PubkeyToAddress(*publicKeyECDSA)
	configuredAddress := common.HexToAddress(walletCfg.Address)
	if senderAddress != configuredAddress {
		glog.Warningf(ctx, "%s Configured address %s does not match address derived from private key %s (%s). Using derived address.",
			logPrefix, walletCfg.Address, walletCfg.PrivateKeyEnvVar, senderAddress.Hex())
		return nil, gerror.Newf("configured address %s does not match derived address %s", walletCfg.Address, senderAddress.Hex())
	}

	// Connect to ETH node
	client, err := ethclient.Dial(rpcCfg.Url)
	if err != nil {
		return nil, gerror.Wrapf(err, "failed to connect to ETH node at %s", rpcCfg.Url)
	}

	// Verify Chain ID if provided
	chainId := big.NewInt(rpcCfg.ChainId)
	networkChainId, err := client.ChainID(ctx)
	if err != nil {
		// Don't fail hard, but log a warning. ChainID is crucial for signing.
		glog.Warningf(ctx, "%s Failed to retrieve chain ID from node %s: %v. Using configured chainId %d.", logPrefix, rpcCfg.Url, err, rpcCfg.ChainId)
	} else if chainId.Cmp(networkChainId) != 0 {
		glog.Warningf(ctx, "%s Configured chainId %d does not match network chainId %d from node %s.", logPrefix, chainId, networkChainId, rpcCfg.Url)
		return nil, gerror.Newf("configured chainId %d does not match network chainId %d", chainId, networkChainId)
	}

	// Load necessary ABIs during initialization
	if err := loadBalanceOfABI(); err != nil {
		// client.Close() // Consider cleanup
		return nil, gerror.Wrap(err, "failed to load balanceOf ABI during sender initialization")
	}
	if err := loadTransferABI(); err != nil {
		// client.Close() // Consider cleanup
		return nil, gerror.Wrap(err, "failed to load transfer ABI during sender initialization")
	}

	// Store the necessary parameters in the struct
	sender := &EthSender{
		client:      client,
		redisClient: redis,
		privateKey:  privateKey,
		publicKey:   senderAddress, // Use the derived address
		chainId:     chainId,
		txParams:    txParamCfg, // Store the modified txParamCfg with parsed decimal
	}

	return sender, nil
}

// Close closes the underlying ethclient connection.
func (s *EthSender) Close() {
	if s.client != nil {
		s.client.Close()
	}
}
