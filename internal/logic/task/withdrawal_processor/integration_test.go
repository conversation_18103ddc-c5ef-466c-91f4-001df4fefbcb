package withdrawal_processor

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	taskv1 "task-withdraw/api"
)

// IntegrationTestHelper provides utilities for integration testing
type IntegrationTestHelper struct {
	*TestHelper
	processedMessages []StatusUpdateMessage
	grpcCalls         []taskv1.UpdateWithdrawalStatusRequest
}

func NewIntegrationTestHelper() *IntegrationTestHelper {
	return &IntegrationTestHelper{
		TestHelper:        NewTestHelper(),
		processedMessages: []StatusUpdateMessage{},
		grpcCalls:         []taskv1.UpdateWithdrawalStatusRequest{},
	}
}

func (h *IntegrationTestHelper) SetupIntegrationMocks() {
	h.SetupMocks()

	// Override mocks to capture calls
	h.mockRedis.On("LPush", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		if len(args) > 2 {
			if msgStr, ok := args[2].(string); ok {
				var msg StatusUpdateMessage
				if err := json.Unmarshal([]byte(msgStr), &msg); err == nil {
					h.processedMessages = append(h.processedMessages, msg)
				}
			}
		}
	}).Return(int64(1), nil)

	h.mockClient.On("UpdateWithdrawalStatus", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		if req, ok := args[1].(*taskv1.UpdateWithdrawalStatusRequest); ok {
			h.grpcCalls = append(h.grpcCalls, *req)
		}
	}).Return(nil)
}

func (h *IntegrationTestHelper) GetProcessedMessages() []StatusUpdateMessage {
	return h.processedMessages
}

func (h *IntegrationTestHelper) GetGrpcCalls() []taskv1.UpdateWithdrawalStatusRequest {
	return h.grpcCalls
}

func (h *IntegrationTestHelper) ClearCapturedData() {
	h.processedMessages = []StatusUpdateMessage{}
	h.grpcCalls = []taskv1.UpdateWithdrawalStatusRequest{}
}

// TestInsufficientBalanceIntegrationFlow tests the complete flow for insufficient balance
func TestInsufficientBalanceIntegrationFlow(t *testing.T) {
	helper := NewIntegrationTestHelper()
	helper.SetupIntegrationMocks()
	defer helper.TearDown()

	t.Run("Complete insufficient balance flow", func(t *testing.T) {
		// Step 1: Create a withdrawal with insufficient balance scenario
		withdrawal := helper.CreateTestWithdrawal(12345, 100.0, "ETH")
		insufficientBalanceError := helper.CreateInsufficientBalanceError("eth")

		// Step 2: Simulate error detection
		errMsgLower := insufficientBalanceError.Error()
		isInsufficientBalance := containsIgnoreCase(errMsgLower, "insufficient") &&
			containsIgnoreCase(errMsgLower, "balance")

		assert.True(t, isInsufficientBalance, "Should detect insufficient balance error")

		// Step 3: Simulate state setting logic
		var finalState int32
		var autoWithdrawalProgress *int32

		if isInsufficientBalance {
			finalState = 2 // State 2: Processing (待冷钱包转入)
			progress := int32(0)
			autoWithdrawalProgress = &progress
		}

		assert.Equal(t, int32(2), finalState, "Final state should be 2 for insufficient balance")
		require.NotNil(t, autoWithdrawalProgress, "AutoWithdrawalProgress should not be nil")
		assert.Equal(t, int32(0), *autoWithdrawalProgress, "AutoWithdrawalProgress should be 0")

		// Step 4: Simulate status update message creation
		msg := StatusUpdateMessage{
			WithdrawalID:           withdrawal.UserWithdrawsId,
			TargetState:            finalState,
			TxHash:                 "",
			ErrorMessage:           `{"insufficient_balance": true, "final": true}`,
			Retries:                0,
			AutoWithdrawalProgress: autoWithdrawalProgress,
		}

		// Step 5: Verify message serialization
		msgBytes, err := json.Marshal(msg)
		require.NoError(t, err, "Should be able to marshal status update message")

		var unmarshaled StatusUpdateMessage
		err = json.Unmarshal(msgBytes, &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal status update message")

		// Step 6: Verify complete message structure
		assert.Equal(t, withdrawal.UserWithdrawsId, unmarshaled.WithdrawalID, "Withdrawal ID should match")
		assert.Equal(t, int32(2), unmarshaled.TargetState, "Target state should be 2")
		assert.Equal(t, "", unmarshaled.TxHash, "TX hash should be empty for insufficient balance")
		assert.Contains(t, unmarshaled.ErrorMessage, "insufficient_balance", "Error message should contain insufficient_balance flag")
		assert.Equal(t, int32(0), unmarshaled.Retries, "Retries should be 0")
		require.NotNil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should not be nil")
		assert.Equal(t, int32(0), *unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should be 0")

		// Step 7: Simulate gRPC request mapping
		grpcReq := &taskv1.UpdateWithdrawalStatusRequest{
			WithdrawalId:           unmarshaled.WithdrawalID,
			ProcessingStatus:       unmarshaled.TargetState,
			TxHash:                 unmarshaled.TxHash,
			ErrorMessage:           unmarshaled.ErrorMessage,
			Retries:                unmarshaled.Retries,
			AutoWithdrawalProgress: *unmarshaled.AutoWithdrawalProgress,
		}

		// Step 8: Verify gRPC request structure
		assert.Equal(t, withdrawal.UserWithdrawsId, grpcReq.WithdrawalId, "gRPC withdrawal ID should match")
		assert.Equal(t, int32(2), grpcReq.ProcessingStatus, "gRPC processing status should be 2")
		assert.Equal(t, "", grpcReq.TxHash, "gRPC TX hash should be empty")
		assert.Contains(t, grpcReq.ErrorMessage, "insufficient_balance", "gRPC error message should contain insufficient_balance flag")
		assert.Equal(t, int32(0), grpcReq.Retries, "gRPC retries should be 0")
		assert.Equal(t, int32(0), grpcReq.AutoWithdrawalProgress, "gRPC auto withdrawal progress should be 0")
	})
}

// TestNormalWithdrawalIntegrationFlow tests the complete flow for normal successful withdrawal
func TestNormalWithdrawalIntegrationFlow(t *testing.T) {
	helper := NewIntegrationTestHelper()
	helper.SetupIntegrationMocks()
	defer helper.TearDown()

	t.Run("Complete successful withdrawal flow", func(t *testing.T) {
		// Step 1: Create a successful withdrawal
		withdrawal := helper.CreateTestWithdrawal(67890, 50.0, "USDT")
		txHash := "0x1234567890abcdef1234567890abcdef12345678"

		// Step 2: Simulate successful processing
		processingStatus := "success"
		var finalState int32 = 4 // State 4: Processing/Completed

		// Step 3: Create status update message
		msg := StatusUpdateMessage{
			WithdrawalID:           withdrawal.UserWithdrawsId,
			TargetState:            finalState,
			TxHash:                 txHash,
			ErrorMessage:           "",
			Retries:                0,
			AutoWithdrawalProgress: nil, // No auto withdrawal progress for successful transactions
		}

		// Step 4: Verify message structure
		msgBytes, err := json.Marshal(msg)
		require.NoError(t, err, "Should be able to marshal successful withdrawal message")

		var unmarshaled StatusUpdateMessage
		err = json.Unmarshal(msgBytes, &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal successful withdrawal message")

		assert.Equal(t, withdrawal.UserWithdrawsId, unmarshaled.WithdrawalID, "Withdrawal ID should match")
		assert.Equal(t, int32(4), unmarshaled.TargetState, "Target state should be 4 for success")
		assert.Equal(t, txHash, unmarshaled.TxHash, "TX hash should match")
		assert.Equal(t, "", unmarshaled.ErrorMessage, "Error message should be empty for success")
		assert.Equal(t, int32(0), unmarshaled.Retries, "Retries should be 0 for success")
		assert.Nil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should be nil for success")

		// Step 5: Verify processing status
		assert.Equal(t, "success", processingStatus, "Processing status should be success")
	})
}

// TestRetryableErrorIntegrationFlow tests the complete flow for retryable errors
func TestRetryableErrorIntegrationFlow(t *testing.T) {
	helper := NewIntegrationTestHelper()
	helper.SetupIntegrationMocks()
	defer helper.TearDown()

	t.Run("Complete retryable error flow", func(t *testing.T) {
		// Step 1: Create a withdrawal with retryable error
		withdrawal := helper.CreateTestWithdrawal(11111, 25.0, "TRX")
		retryableError := helper.CreateNonInsufficientBalanceError("network_error")
		currentRetries := int32(1)
		maxAttempts := int32(3)

		// Step 2: Simulate error detection
		isInsufficientBalance := false
		isNonRetryable := false // Network errors are retryable

		// Step 3: Simulate retry logic
		var finalState int32
		var shouldRetry bool

		if !isInsufficientBalance && !isNonRetryable && currentRetries < maxAttempts {
			finalState = 1 // State 1: Pending (for retry)
			shouldRetry = true
		}

		assert.Equal(t, int32(1), finalState, "Final state should be 1 for retry")
		assert.True(t, shouldRetry, "Should retry for retryable errors under max attempts")

		// Step 4: Create retry status update message
		newRetries := currentRetries + 1
		msg := StatusUpdateMessage{
			WithdrawalID:           withdrawal.UserWithdrawsId,
			TargetState:            finalState,
			TxHash:                 "",
			ErrorMessage:           `{"error": "network timeout", "retry": ` + string(rune(newRetries)) + `}`,
			Retries:                newRetries,
			AutoWithdrawalProgress: nil,
		}

		// Step 5: Verify retry message structure
		msgBytes, err := json.Marshal(msg)
		require.NoError(t, err, "Should be able to marshal retry message")

		var unmarshaled StatusUpdateMessage
		err = json.Unmarshal(msgBytes, &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal retry message")

		assert.Equal(t, withdrawal.UserWithdrawsId, unmarshaled.WithdrawalID, "Withdrawal ID should match")
		assert.Equal(t, int32(1), unmarshaled.TargetState, "Target state should be 1 for retry")
		assert.Equal(t, "", unmarshaled.TxHash, "TX hash should be empty for retry")
		assert.Contains(t, unmarshaled.ErrorMessage, "retry", "Error message should contain retry information")
		assert.Equal(t, newRetries, unmarshaled.Retries, "Retries should be incremented")
		assert.Nil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should be nil for retry")

		// Step 6: Verify error message
		assert.Equal(t, retryableError.Error(), "network connection failed", "Original error should be network related")
	})
}

// TestNonRetryableErrorIntegrationFlow tests the complete flow for non-retryable errors
func TestNonRetryableErrorIntegrationFlow(t *testing.T) {
	helper := NewIntegrationTestHelper()
	helper.SetupIntegrationMocks()
	defer helper.TearDown()

	t.Run("Complete non-retryable error flow", func(t *testing.T) {
		// Step 1: Create a withdrawal with non-retryable error
		withdrawal := helper.CreateTestWithdrawal(22222, 75.0, "ETH")
		nonRetryableError := helper.CreateNonInsufficientBalanceError("invalid_address")

		// Step 2: Simulate error detection
		isInsufficientBalance := false
		isNonRetryable := true // Invalid address is non-retryable

		// Step 3: Simulate non-retryable logic
		var finalState int32 = 5 // State 5: Failed

		assert.Equal(t, int32(5), finalState, "Final state should be 5 for non-retryable errors")

		// Step 4: Create failed status update message
		msg := StatusUpdateMessage{
			WithdrawalID:           withdrawal.UserWithdrawsId,
			TargetState:            finalState,
			TxHash:                 "",
			ErrorMessage:           `{"error": "invalid address format", "final": true}`,
			Retries:                0,
			AutoWithdrawalProgress: nil,
		}

		// Step 5: Verify failed message structure
		msgBytes, err := json.Marshal(msg)
		require.NoError(t, err, "Should be able to marshal failed message")

		var unmarshaled StatusUpdateMessage
		err = json.Unmarshal(msgBytes, &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal failed message")

		assert.Equal(t, withdrawal.UserWithdrawsId, unmarshaled.WithdrawalID, "Withdrawal ID should match")
		assert.Equal(t, int32(5), unmarshaled.TargetState, "Target state should be 5 for failure")
		assert.Equal(t, "", unmarshaled.TxHash, "TX hash should be empty for failure")
		assert.Contains(t, unmarshaled.ErrorMessage, "final", "Error message should contain final flag")
		assert.Equal(t, int32(0), unmarshaled.Retries, "Retries should be 0 for non-retryable")
		assert.Nil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should be nil for failure")

		// Step 6: Verify error details
		assert.Equal(t, nonRetryableError.Error(), "invalid address format", "Original error should be address related")
		assert.True(t, isNonRetryable, "Should be detected as non-retryable")
		assert.False(t, isInsufficientBalance, "Should not be detected as insufficient balance")
	})
}
