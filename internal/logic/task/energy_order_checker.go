package task

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"

	wp "task-withdraw/internal/logic/task/withdrawal_processor"
	"task-withdraw/internal/service"
)

// EnergyOrderChecker handles background checking of energy orders
type EnergyOrderChecker struct {
	energyManager *wp.EnergyManager
	config        *wp.EnergyManagerConfig
	isRunning     bool
}

// NewEnergyOrderChecker creates a new energy order checker
func NewEnergyOrderChecker() *EnergyOrderChecker {
	energyConfig := wp.GetDefaultEnergyManagerConfig()
	ctx := context.Background()
	
	// Load configuration directly from GoFrame config file
	cfg := g.Cfg()
	
	// Update config from file
	energyConfig.Enabled = cfg.MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.enabled", energyConfig.Enabled).Bool()
	energyConfig.EnergyReservePercent = cfg.MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.energyReservePercent", energyConfig.EnergyReservePercent).Float64()
	energyConfig.OrderTimeoutHours = cfg.MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.orderTimeoutHours", energyConfig.OrderTimeoutHours).Int()
	energyConfig.MaxRetryCount = cfg.MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.maxRetryCount", energyConfig.MaxRetryCount).Int()
	energyConfig.CheckIntervalSeconds = cfg.MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.checkIntervalSeconds", energyConfig.CheckIntervalSeconds).Int()

	energyManager := wp.NewEnergyManager(energyConfig)
	
	return &EnergyOrderChecker{
		energyManager: energyManager,
		config:        energyConfig,
		isRunning:     false,
	}
}

// Start starts the background energy order checking task
func (eoc *EnergyOrderChecker) Start(ctx context.Context) {
	logPrefix := "[EnergyOrderChecker.Start]"
	
	if !eoc.config.Enabled {
		glog.Infof(ctx, "%s Energy order checker is disabled in configuration", logPrefix)
		return
	}
	
	if eoc.isRunning {
		glog.Warningf(ctx, "%s Energy order checker is already running", logPrefix)
		return
	}
	
	eoc.isRunning = true
	glog.Infof(ctx, "%s Starting energy order checker with %d second intervals", 
		logPrefix, eoc.config.CheckIntervalSeconds)
	
	go eoc.runCheckLoop(ctx)
}

// Stop stops the background checking task
func (eoc *EnergyOrderChecker) Stop(ctx context.Context) {
	logPrefix := "[EnergyOrderChecker.Stop]"
	glog.Infof(ctx, "%s Stopping energy order checker", logPrefix)
	eoc.isRunning = false
}

// runCheckLoop runs the main checking loop
func (eoc *EnergyOrderChecker) runCheckLoop(ctx context.Context) {
	logPrefix := "[EnergyOrderChecker.runCheckLoop]"
	
	ticker := time.NewTicker(time.Duration(eoc.config.CheckIntervalSeconds) * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			glog.Infof(ctx, "%s Context cancelled, stopping energy order checker", logPrefix)
			eoc.isRunning = false
			return
		case <-ticker.C:
			if !eoc.isRunning {
				glog.Infof(ctx, "%s Energy order checker stopped, exiting loop", logPrefix)
				return
			}
			
			// Check pending orders
			eoc.checkPendingOrders(ctx)
		}
	}
}

// checkPendingOrders checks all pending energy orders
func (eoc *EnergyOrderChecker) checkPendingOrders(ctx context.Context) {
	logPrefix := "[EnergyOrderChecker.checkPendingOrders]"
	
	// Get iTRX API configuration
	apiKey, _, apiBaseUrl, err := eoc.getItrxConfig(ctx)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to get iTRX API config: %v", logPrefix, err)
		return
	}
	
	// Get list of pending order IDs
	client := service.Redis().Client()
	orderIDs, err := client.LRange(ctx, wp.RedisKeyPendingOrders, 0, -1)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to get pending orders list: %v", logPrefix, err)
		return
	}
	
	if len(orderIDs) == 0 {
		glog.Debugf(ctx, "%s No pending energy orders to check", logPrefix)
		return
	}
	
	glog.Debugf(ctx, "%s Checking %d pending energy orders", logPrefix, len(orderIDs))
	
	checkedCount := 0
	updatedCount := 0
	
	// Check each order
	for _, orderIDVar := range orderIDs {
		orderID := orderIDVar.String()
		
		// Get order details
		order, err := eoc.energyManager.GetEnergyOrder(ctx, orderID)
		if err != nil {
			glog.Errorf(ctx, "%s Failed to get order %s: %v", logPrefix, orderID, err)
			continue
		}
		
		if order == nil {
			// Order not found, remove from pending list
			client.LRem(ctx, wp.RedisKeyPendingOrders, 0, orderID)
			glog.Warningf(ctx, "%s Order %s not found, removed from pending list", logPrefix, orderID)
			continue
		}
		
		// Check if order is expired
		if time.Now().After(order.ExpireAt) {
			eoc.energyManager.UpdateEnergyOrderStatus(ctx, orderID, wp.EnergyOrderStatusExpired, "order expired")
			eoc.energyManager.RemoveEnergyOrder(ctx, orderID)
			glog.Infof(ctx, "%s Order %s expired, removed", logPrefix, orderID)
			updatedCount++
			continue
		}
		
		// Skip if recently checked
		if time.Since(order.LastCheckAt) < time.Duration(eoc.config.CheckIntervalSeconds/2)*time.Second {
			continue
		}
		
		// Check order status via API  
		status, err := eoc.energyManager.CheckEnergyOrderStatus(ctx, orderID, apiKey, apiBaseUrl)
		if err != nil {
			glog.Errorf(ctx, "%s Failed to check status for order %s: %v", logPrefix, orderID, err)
			
			// Increment retry count
			order.RetryCount++
			if order.RetryCount >= eoc.config.MaxRetryCount {
				// Mark as failed
				eoc.energyManager.UpdateEnergyOrderStatus(ctx, orderID, wp.EnergyOrderStatusFailed, 
					"max retries reached")
				eoc.energyManager.RemoveEnergyOrder(ctx, orderID)
				glog.Warningf(ctx, "%s Order %s failed after max retries", logPrefix, orderID)
				updatedCount++
			} else {
				// Update retry count
				eoc.energyManager.UpdateEnergyOrderStatus(ctx, orderID, wp.EnergyOrderStatusPending, 
					err.Error())
			}
			continue
		}
		
		checkedCount++
		
		// Update status if changed
		if status != order.Status {
			eoc.energyManager.UpdateEnergyOrderStatus(ctx, orderID, status, "")
			glog.Infof(ctx, "%s Order %s status updated: %s -> %s", 
				logPrefix, orderID, order.Status, status)
			updatedCount++
			
			// Remove completed or failed orders
			if status == wp.EnergyOrderStatusCompleted || status == wp.EnergyOrderStatusFailed {
				// Don't remove completed orders immediately, let the main process handle cleanup
				// Only remove failed orders
				if status == wp.EnergyOrderStatusFailed {
					eoc.energyManager.RemoveEnergyOrder(ctx, orderID)
					glog.Infof(ctx, "%s Removed failed order %s", logPrefix, orderID)
				}
			}
		}
	}
	
	if checkedCount > 0 || updatedCount > 0 {
		glog.Infof(ctx, "%s Energy order check completed: checked=%d, updated=%d", 
			logPrefix, checkedCount, updatedCount)
	}
}

// getItrxConfig gets iTRX API configuration
func (eoc *EnergyOrderChecker) getItrxConfig(ctx context.Context) (apiKey, apiSecret, apiBaseUrl string, err error) {
	// Read iTRX configuration directly from GoFrame config file
	apiKey = g.Cfg().MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.itrx.apiKey", "").String()
	apiSecret = g.Cfg().MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.itrx.apiSecret", "").String()
	apiBaseUrl = g.Cfg().MustGet(ctx, "withdrawalProcessor.trc20EnergyManagement.itrx.apiBaseUrl", "https://itrx.io").String()
	
	if apiKey == "" || apiSecret == "" {
		return "", "", "", fmt.Errorf("iTRX API credentials not configured")
	}
	
	return apiKey, apiSecret, apiBaseUrl, nil
}

// GetStats returns statistics about energy orders
func (eoc *EnergyOrderChecker) GetStats(ctx context.Context) map[string]interface{} {
	logPrefix := "[EnergyOrderChecker.GetStats]"
	
	client := service.Redis().Client()
	
	// Count pending orders
	pendingCount, err := client.LLen(ctx, wp.RedisKeyPendingOrders)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to get pending orders count: %v", logPrefix, err)
		pendingCount = 0
	}
	
	stats := map[string]interface{}{
		"enabled":        eoc.config.Enabled,
		"running":        eoc.isRunning,
		"pending_orders": pendingCount,
		"check_interval": eoc.config.CheckIntervalSeconds,
		"max_retries":    eoc.config.MaxRetryCount,
		"config":         eoc.config,
	}
	
	return stats
}