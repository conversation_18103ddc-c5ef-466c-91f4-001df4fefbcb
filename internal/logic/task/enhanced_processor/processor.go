package enhanced_processor

import (
	"context"
	"encoding/json"
	"time"

	"task-withdraw/internal/config"
	"task-withdraw/internal/model"
	"task-withdraw/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// ProcessPendingTransactions is the enhanced processor that handles all transaction types
func ProcessPendingTransactions(ctx context.Context) {
	logPrefix := "[EnhancedTransactionProcessor]"
	glog.Infof(ctx, "%s Task started.", logPrefix)
	startTime := time.Now()

	// Check if auto withdrawal is enabled
	autoWithdrawalEnabled, err := config.GetBool(ctx, "auto_withdrawal_setting.state", false)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to get auto_withdrawal_setting.state: %v. Assuming disabled.", logPrefix, err)
		autoWithdrawalEnabled = false
	}

	if !autoWithdrawalEnabled {
		glog.Warningf(ctx, "%s Auto withdrawal is disabled (auto_withdrawal_setting.state=false). Skipping task execution.", logPrefix)
		return
	}

	// Load configuration
	cfg, err := LoadConfig(ctx)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to load configuration: %v. Task cannot proceed.", logPrefix, err)
		return
	}
	if !cfg.Enabled {
		glog.Infof(ctx, "%s Task is disabled in configuration. Exiting.", logPrefix)
		return
	}

	// Get gRPC Client from Service Registry
	withdrawalClient := service.WithdrawalClient()

	// Process each transaction type
	totalEnqueued := 0
	totalFailed := 0

	// 1. Process User Withdrawals
	if cfg.TransactionTypes.UserWithdrawals.Enabled {
		enqueued, failed := processUserWithdrawals(ctx, withdrawalClient, cfg)
		totalEnqueued += enqueued
		totalFailed += failed
	}

	// 2. Process Merchant Withdrawals
	if cfg.TransactionTypes.MerchantWithdrawals.Enabled {
		enqueued, failed := processMerchantWithdrawals(ctx, withdrawalClient, cfg)
		totalEnqueued += enqueued
		totalFailed += failed
	}

	// 3. Process Merchant Settlements
	if cfg.TransactionTypes.MerchantSettlements.Enabled {
		enqueued, failed := processMerchantSettlements(ctx, withdrawalClient, cfg)
		totalEnqueued += enqueued
		totalFailed += failed
	}

	// Final summary
	glog.Infof(ctx, "%s Task finished. Total Enqueued: %d, Total Failed: %d. Duration: %s",
		logPrefix, totalEnqueued, totalFailed, time.Since(startTime))
}

// processUserWithdrawals handles user withdrawal processing
func processUserWithdrawals(ctx context.Context, client service.IWithdrawalClient, cfg *EnhancedConfig) (enqueued, failed int) {
	logPrefix := "[UserWithdrawals]"
	batchSize := cfg.TransactionTypes.UserWithdrawals.BatchSize
	if batchSize <= 0 {
		batchSize = cfg.BatchSize // fallback to global batch size
	}

	glog.Infof(ctx, "%s Processing user withdrawals with batch size: %d", logPrefix, batchSize)

	// Fetch pending user withdrawals
	pendingWithdrawals, err := client.FetchPendingWithdrawals(ctx, batchSize)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to fetch pending user withdrawals: %v", logPrefix, err)
		return 0, 0
	}

	if len(pendingWithdrawals) == 0 {
		glog.Infof(ctx, "%s No pending user withdrawals found.", logPrefix)
		return 0, 0
	}

	glog.Infof(ctx, "%s Found %d pending user withdrawals to enqueue.", logPrefix, len(pendingWithdrawals))

	// Enqueue each withdrawal
	for _, withdrawal := range pendingWithdrawals {
		if withdrawal == nil {
			continue
		}

		transactionReq := model.NewUserWithdrawalRequest(withdrawal)
		if enqueueTransaction(ctx, transactionReq, cfg.QueueName) {
			enqueued++
		} else {
			failed++
		}
	}

	glog.Infof(ctx, "%s Processed user withdrawals. Enqueued: %d, Failed: %d", logPrefix, enqueued, failed)
	return enqueued, failed
}

// processMerchantWithdrawals handles merchant withdrawal processing
func processMerchantWithdrawals(ctx context.Context, client service.IWithdrawalClient, cfg *EnhancedConfig) (enqueued, failed int) {
	logPrefix := "[MerchantWithdrawals]"
	batchSize := cfg.TransactionTypes.MerchantWithdrawals.BatchSize
	if batchSize <= 0 {
		batchSize = cfg.BatchSize // fallback to global batch size
	}

	glog.Infof(ctx, "%s Processing merchant withdrawals with batch size: %d", logPrefix, batchSize)

	// Fetch pending merchant withdrawals
	pendingWithdrawals, err := client.FetchPendingMerchantWithdrawals(ctx, batchSize)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to fetch pending merchant withdrawals: %v", logPrefix, err)
		return 0, 0
	}

	if len(pendingWithdrawals) == 0 {
		glog.Infof(ctx, "%s No pending merchant withdrawals found.", logPrefix)
		return 0, 0
	}

	glog.Infof(ctx, "%s Found %d pending merchant withdrawals to enqueue.", logPrefix, len(pendingWithdrawals))

	// Enqueue each withdrawal
	for _, withdrawal := range pendingWithdrawals {
		if withdrawal == nil {
			continue
		}

		transactionReq := model.NewMerchantWithdrawalRequest(withdrawal)
		if enqueueTransaction(ctx, transactionReq, cfg.QueueName) {
			enqueued++
		} else {
			failed++
		}
	}

	glog.Infof(ctx, "%s Processed merchant withdrawals. Enqueued: %d, Failed: %d", logPrefix, enqueued, failed)
	return enqueued, failed
}

// processMerchantSettlements handles merchant settlement processing
func processMerchantSettlements(ctx context.Context, client service.IWithdrawalClient, cfg *EnhancedConfig) (enqueued, failed int) {
	logPrefix := "[MerchantSettlements]"
	batchSize := cfg.TransactionTypes.MerchantSettlements.BatchSize
	if batchSize <= 0 {
		batchSize = cfg.BatchSize // fallback to global batch size
	}

	glog.Infof(ctx, "%s Processing merchant settlements with batch size: %d", logPrefix, batchSize)

	// Fetch pending merchant settlements
	pendingSettlements, err := client.FetchPendingMerchantSettlements(ctx, batchSize)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to fetch pending merchant settlements: %v", logPrefix, err)
		return 0, 0
	}

	if len(pendingSettlements) == 0 {
		glog.Infof(ctx, "%s No pending merchant settlements found.", logPrefix)
		return 0, 0
	}

	glog.Infof(ctx, "%s Found %d pending merchant settlements to enqueue.", logPrefix, len(pendingSettlements))

	// Enqueue each settlement
	for _, settlement := range pendingSettlements {
		if settlement == nil {
			continue
		}

		transactionReq := model.NewMerchantSettlementRequest(settlement)
		if enqueueTransaction(ctx, transactionReq, cfg.QueueName) {
			enqueued++
		} else {
			failed++
		}
	}

	glog.Infof(ctx, "%s Processed merchant settlements. Enqueued: %d, Failed: %d", logPrefix, enqueued, failed)
	return enqueued, failed
}

// enqueueTransaction enqueues a transaction request to Redis
func enqueueTransaction(ctx context.Context, transactionReq *model.TransactionRequest, queueName string) bool {
	logPrefix := "[EnqueueTransaction]"

	// Validate transaction request
	if err := transactionReq.Validate(); err != nil {
		glog.Errorf(ctx, "%s Invalid transaction request: %v", logPrefix, err)
		return false
	}

	// Serialize transaction request to JSON
	transactionBytes, err := json.Marshal(transactionReq)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to marshal transaction request: %v", logPrefix, err)
		return false
	}

	// Get Redis client
	redisClient := service.Redis().Client()

	// LPUSH the JSON string to the processing queue
	_, err = redisClient.LPush(ctx, queueName, string(transactionBytes))
	if err != nil {
		glog.Errorf(ctx, "%s Failed to LPUSH transaction to Redis queue '%s': %v", logPrefix, queueName, err)
		return false
	}

	glog.Debugf(ctx, "%s Successfully enqueued %s transaction ID %d to queue '%s'",
		logPrefix, transactionReq.Type, transactionReq.GetTransactionID(), queueName)
	return true
}

// TransactionTypeConfig represents configuration for a specific transaction type
type TransactionTypeConfig struct {
	Enabled   bool `json:"enabled"`
	BatchSize int  `json:"batchSize"`
}

// TransactionTypesConfig represents configuration for all transaction types
type TransactionTypesConfig struct {
	UserWithdrawals     TransactionTypeConfig `json:"userWithdrawals"`
	MerchantWithdrawals TransactionTypeConfig `json:"merchantWithdrawals"`
	MerchantSettlements TransactionTypeConfig `json:"merchantSettlements"`
}

// EnhancedConfig represents the enhanced processor configuration
type EnhancedConfig struct {
	Enabled          bool                   `json:"enabled"`
	BatchSize        int                    `json:"batchSize"`
	QueueName        string                 `json:"queueName"`
	TransactionTypes TransactionTypesConfig `json:"transactionTypes"`
}

// LoadConfig loads the enhanced processor configuration
func LoadConfig(ctx context.Context) (*EnhancedConfig, error) {
	cfg := &EnhancedConfig{}

	// Load basic configuration
	cfg.Enabled = g.Cfg().MustGet(ctx, "withdrawalProcessor.enabled", true).Bool()
	cfg.BatchSize = g.Cfg().MustGet(ctx, "withdrawalProcessor.batchSize", 50).Int()
	cfg.QueueName = g.Cfg().MustGet(ctx, "withdrawalConsumer.redisQueueName", "queue:transaction_processing").String()

	// Load transaction type configurations
	cfg.TransactionTypes.UserWithdrawals.Enabled = g.Cfg().MustGet(ctx, "withdrawalProcessor.transactionTypes.userWithdrawals.enabled", true).Bool()
	cfg.TransactionTypes.UserWithdrawals.BatchSize = g.Cfg().MustGet(ctx, "withdrawalProcessor.transactionTypes.userWithdrawals.batchSize", 50).Int()

	cfg.TransactionTypes.MerchantWithdrawals.Enabled = g.Cfg().MustGet(ctx, "withdrawalProcessor.transactionTypes.merchantWithdrawals.enabled", true).Bool()
	cfg.TransactionTypes.MerchantWithdrawals.BatchSize = g.Cfg().MustGet(ctx, "withdrawalProcessor.transactionTypes.merchantWithdrawals.batchSize", 30).Int()

	cfg.TransactionTypes.MerchantSettlements.Enabled = g.Cfg().MustGet(ctx, "withdrawalProcessor.transactionTypes.merchantSettlements.enabled", true).Bool()
	cfg.TransactionTypes.MerchantSettlements.BatchSize = g.Cfg().MustGet(ctx, "withdrawalProcessor.transactionTypes.merchantSettlements.batchSize", 20).Int()

	return cfg, nil
}
