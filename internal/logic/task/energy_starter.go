package task

import (
	"context"
	"sync"

	"github.com/gogf/gf/v2/os/glog"
)

var (
	energyCheckerInstance *EnergyOrderChecker
	energyCheckerOnce     sync.Once
)

// StartEnergyOrder<PERSON><PERSON><PERSON> starts the global energy order checker
func StartEnergyOrderChecker(ctx context.Context) {
	energyCheckerOnce.Do(func() {
		logPrefix := "[StartEnergyOrderChecker]"
		glog.Infof(ctx, "%s Initializing energy order checker", logPrefix)
		
		energyCheckerInstance = NewEnergyOrderChecker()
		energyCheckerInstance.Start(ctx)
		
		glog.Infof(ctx, "%s Energy order checker started successfully", logPrefix)
	})
}

// StopEnergyOrder<PERSON>he<PERSON> stops the global energy order checker
func StopEnergyOrderChecker(ctx context.Context) {
	if energyCheckerInstance != nil {
		energyCheckerInstance.Stop(ctx)
	}
}

// GetEnergyOrderCheckerStats returns statistics about the energy order checker
func GetEnergyOrderCheckerStats(ctx context.Context) map[string]interface{} {
	if energyCheckerInstance != nil {
		return energyCheckerInstance.GetStats(ctx)
	}
	return map[string]interface{}{
		"enabled": false,
		"running": false,
		"message": "energy order checker not initialized",
	}
}