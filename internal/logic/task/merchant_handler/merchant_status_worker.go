package merchant_handler

import (
	"context"
	"time"

	"task-withdraw/internal/service"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/database/gredis"
)

// StartMerchantStatusWorker starts a worker that processes merchant status updates
func StartMerchantStatusWorker(ctx context.Context) {
	logPrefix := "[MerchantStatusWorker]"
	glog.Infof(ctx, "%s Starting merchant status update worker...", logPrefix)
	
	// Get configuration
	const cfgRedisQueueNameKey = "grpcUpdater.merchantRedisQueueName"
	queueName := g.Cfg().MustGet(ctx, cfgRedisQueueNameKey, "queue:merchant_status_update").String()
	
	const cfgDLQNameKey = "grpcUpdater.merchantDLQName"
	dlqName := g.Cfg().MustGet(ctx, cfgDLQNameKey, "queue:merchant_status_update_dlq").String()
	
	const cfgTimeoutKey = "grpcUpdater.merchantTimeout"
	timeout := g.Cfg().MustGet(ctx, cfgTimeoutKey, 5).Duration() * time.Second
	
	const cfgMaxRetriesKey = "grpcUpdater.merchantMaxRetries"
	maxRetries := g.Cfg().MustGet(ctx, cfgMaxRetriesKey, 3).Int()
	
	glog.Infof(ctx, "%s Worker configured: Queue='%s', DLQ='%s', Timeout=%s, MaxRetries=%d",
		logPrefix, queueName, dlqName, timeout, maxRetries)
	
	redisClient := service.Redis().Client()
	
	// Start worker goroutine
	go func() {
		glog.Infof(ctx, "%s Worker goroutine started.", logPrefix)
		defer func() {
			glog.Infof(ctx, "%s Worker goroutine stopped.", logPrefix)
		}()
		
		retryCount := make(map[string]int)
		
		for {
			select {
			case <-ctx.Done():
				glog.Infof(ctx, "%s Context cancelled. Worker stopping...", logPrefix)
				return
			default:
				// Try to get a message from the queue with timeout
				result, err := redisClient.BRPop(ctx, int64(timeout.Seconds()), queueName)
				if err != nil {
					if err.Error() != "redis: nil" {
						glog.Errorf(ctx, "%s BRPop error: %v", logPrefix, err)
					}
					continue
				}
				
				// BRPop returns a Vars array with [queue_name, message]
				if result == nil || len(result) < 2 {
					continue
				}
				
				// Get the message (second element)
				message := result[1].String()
				if message == "" {
					continue
				}
				glog.Debugf(ctx, "%s Received message from queue '%s': %s", logPrefix, queueName, message)
				
				// Process the message
				err = ProcessMerchantStatusUpdate(ctx, message)
				if err != nil {
					glog.Errorf(ctx, "%s Failed to process merchant status update: %v", logPrefix, err)
					
					// Handle retry logic
					retries := retryCount[message]
					retries++
					retryCount[message] = retries
					
					if retries < maxRetries {
						glog.Warningf(ctx, "%s Retrying message (attempt %d/%d) after delay...", 
							logPrefix, retries, maxRetries)
						time.Sleep(time.Second * time.Duration(retries)) // Exponential backoff
						
						// Push back to queue for retry
						_, pushErr := redisClient.LPush(ctx, queueName, message)
						if pushErr != nil {
							glog.Errorf(ctx, "%s Failed to push message back to queue for retry: %v", 
								logPrefix, pushErr)
							MoveToDLQ(ctx, redisClient, dlqName, message, "retry_push_failed")
						}
					} else {
						glog.Errorf(ctx, "%s Max retries reached. Moving message to DLQ '%s'", 
							logPrefix, dlqName)
						MoveToDLQ(ctx, redisClient, dlqName, message, "max_retries_reached")
						delete(retryCount, message) // Clean up retry count
					}
				} else {
					// Success - clean up retry count if exists
					delete(retryCount, message)
					glog.Infof(ctx, "%s Successfully processed merchant status update", logPrefix)
				}
			}
		}
	}()
	
	glog.Infof(ctx, "%s Merchant status update worker started successfully.", logPrefix)
}

// MoveToDLQ moves a failed message to the dead letter queue
func MoveToDLQ(ctx context.Context, redisClient *gredis.Redis, dlqName, message, reason string) {
	logPrefix := "[MoveToDLQ]"
	
	// Create DLQ entry with metadata
	dlqEntry := map[string]interface{}{
		"message":   message,
		"reason":    reason,
		"timestamp": time.Now().Format(time.RFC3339),
	}
	
	dlqMessage, err := gjson.Marshal(dlqEntry)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to marshal DLQ entry: %v", logPrefix, err)
		return
	}
	
	_, err = redisClient.LPush(ctx, dlqName, string(dlqMessage))
	if err != nil {
		glog.Errorf(ctx, "%s Failed to push message to DLQ '%s': %v", logPrefix, dlqName, err)
	} else {
		glog.Infof(ctx, "%s Message moved to DLQ '%s' with reason: %s", logPrefix, dlqName, reason)
	}
}