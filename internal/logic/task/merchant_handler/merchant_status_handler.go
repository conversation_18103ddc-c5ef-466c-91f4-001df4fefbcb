package merchant_handler

import (
	"context"
	"encoding/json"
	"fmt"

	taskv1 "task-withdraw/api"
	"task-withdraw/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// MerchantStatusUpdateMessage defines the structure for merchant status update messages
type MerchantStatusUpdateMessage struct {
	TransactionType string `json:"transaction_type"` // "merchant_withdrawal" or "merchant_settlement"
	TransactionID   int64  `json:"transaction_id"`
	State           int32  `json:"state"` // Target state for merchant transaction
	TxHash          string `json:"tx_hash,omitempty"`
	ErrorMessage    string `json:"error_message,omitempty"`
	Retries         int32  `json:"retries,omitempty"`
}

// PushMerchantStatusUpdate pushes a merchant status update to Redis queue
func PushMerchantStatusUpdate(ctx context.Context, transactionType string, transactionID int64, state int32, txHash, errorMessage string, retries int32) error {
	logPrefix := fmt.Sprintf("[PushMerchantStatusUpdate:%s:%d]", transactionType, transactionID)

	// Get queue name from config
	const cfgRedisQueueNameKey = "grpcUpdater.merchantRedisQueueName"
	// Fall back to the same queue as user withdrawals if not configured separately
	queueName := g.Cfg().MustGet(ctx, cfgRedisQueueNameKey, "queue:merchant_status_update").String()

	if queueName == "" {
		return gerror.Newf("Redis queue name ('%s') for merchant status updates is not configured or empty", cfgRedisQueueNameKey)
	}

	msg := MerchantStatusUpdateMessage{
		TransactionType: transactionType,
		TransactionID:   transactionID,
		State:           state,
		TxHash:          txHash,
		ErrorMessage:    errorMessage,
		Retries:         retries,
	}

	msgBytes, err := json.Marshal(msg)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to marshal merchant status update message: %v", logPrefix, err)
		return gerror.Wrap(err, "failed to marshal merchant status update message")
	}

	redisClient := service.Redis().Client()
	_, err = redisClient.LPush(ctx, queueName, string(msgBytes))
	if err != nil {
		glog.Errorf(ctx, "%s Failed to LPush merchant status update message to Redis queue '%s': %v", logPrefix, queueName, err)
		return gerror.Wrapf(err, "failed to LPush merchant status update to Redis queue '%s'", queueName)
	}

	glog.Infof(ctx, "%s Successfully pushed merchant status update message to Redis queue '%s' for state %d", logPrefix, queueName, state)
	return nil
}

// ProcessMerchantStatusUpdate processes a merchant status update message from Redis
func ProcessMerchantStatusUpdate(ctx context.Context, message string) error {
	logPrefix := "[ProcessMerchantStatusUpdate]"
	
	var msg MerchantStatusUpdateMessage
	if err := json.Unmarshal([]byte(message), &msg); err != nil {
		glog.Errorf(ctx, "%s Failed to unmarshal message: %v", logPrefix, err)
		return err
	}

	// Add transaction info to log prefix
	logPrefix = fmt.Sprintf("%s[%s:%d]", logPrefix, msg.TransactionType, msg.TransactionID)

	// Get withdrawal client
	withdrawalClient := service.WithdrawalClient()

	var err error
	switch msg.TransactionType {
	case "merchant_withdrawal":
		req := &taskv1.UpdateMerchantWithdrawRequest{
			WithdrawsId:  msg.TransactionID,
			State:        msg.State,
			TxHash:       msg.TxHash,
			ErrorMessage: msg.ErrorMessage,
			Retries:      msg.Retries,
		}
		
		glog.Infof(ctx, "%s Updating merchant withdrawal status to state %d", logPrefix, msg.State)
		err = withdrawalClient.UpdateMerchantWithdrawStatus(ctx, req)
		
	case "merchant_settlement":
		req := &taskv1.UpdateMerchantSettlementRequest{
			SettlementsId: msg.TransactionID,
			State:         msg.State,
			TxHash:        msg.TxHash,
			ErrorMessage:  msg.ErrorMessage,
			Retries:       msg.Retries,
		}
		
		glog.Infof(ctx, "%s Updating merchant settlement status to state %d", logPrefix, msg.State)
		err = withdrawalClient.UpdateMerchantSettlementStatus(ctx, req)
		
	default:
		err = gerror.Newf("unknown transaction type: %s", msg.TransactionType)
	}

	if err != nil {
		glog.Errorf(ctx, "%s Failed to update merchant status: %v", logPrefix, err)
		return err
	}

	glog.Infof(ctx, "%s Successfully updated merchant status to state %d", logPrefix, msg.State)
	return nil
}

// MapUserStatusToMerchantState maps user withdrawal status back to merchant state
func MapUserStatusToMerchantState(processingStatus int32) int32 {
	switch processingStatus {
	case 0: // 未开始处理
		return 1 // 待审核
	case 1: // 自动放币处理中
		return 2 // 处理中
	case 2: // 待冷钱包转入
		return 2 // 处理中（保持不变，等待重试）
	case 4: // 成功
		return 4 // 已完成
	case 5: // 失败
		return 5 // 失败
	default:
		return 2 // 默认处理中
	}
}