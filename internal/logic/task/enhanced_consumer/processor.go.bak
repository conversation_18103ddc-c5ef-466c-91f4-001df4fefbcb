package enhanced_consumer

import (
	"context"
	"encoding/json"
	"fmt"

	"task-withdraw/internal/model"
	wp "task-withdraw/internal/logic/task/withdrawal_processor"
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/eth"
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/tron"
	"task-withdraw/internal/logic/task/merchant_handler"
	taskv1 "task-withdraw/api"

	"github.com/gogf/gf/v2/os/glog"
)

// ProcessEnhancedMessage handles the unified transaction message processing
func ProcessEnhancedMessage(
	ctx context.Context,
	logPrefix string,
	message string,
	dlqName string,
	processorCfg *wp.WithdrawalConfig,
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) {
	// Add panic recovery for safety
	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic recovered during enhanced message processing: %v", r)
			glog.Criticalf(ctx, "%s %v", logPrefix, err)
			MoveToDlq(ctx, logPrefix, message, dlqName, err)
		}
	}()

	// Parse the unified transaction request
	var transactionReq model.TransactionRequest
	if err := json.Unmarshal([]byte(message), &transactionReq); err != nil {
		glog.Errorf(ctx, "%s Failed to unmarshal transaction request: %v. Message: %s", logPrefix, err, message)
		MoveToDlq(ctx, logPrefix, message, dlqName, err)
		return
	}

	// Validate the transaction request
	if err := transactionReq.Validate(); err != nil {
		glog.Errorf(ctx, "%s Invalid transaction request: %v", logPrefix, err)
		MoveToDlq(ctx, logPrefix, message, dlqName, err)
		return
	}

	// Create enhanced log prefix with transaction info
	enhancedLogPrefix := fmt.Sprintf("%s[%s][ID:%d][OrderNo:%s]", 
		logPrefix, transactionReq.Type, transactionReq.GetTransactionID(), transactionReq.GetOrderNo())
	
	glog.Infof(ctx, "%s Start processing transaction. Retries so far: %d", 
		enhancedLogPrefix, transactionReq.GetRetries())

	// Route to appropriate processor based on transaction type
	switch transactionReq.Type {
	case model.TransactionTypeUserWithdrawal:
		processUserWithdrawal(ctx, enhancedLogPrefix, transactionReq.UserWithdrawal, dlqName, processorCfg, limiter, ethSender, tronSender)
	case model.TransactionTypeMerchantWithdrawal:
		processMerchantWithdrawal(ctx, enhancedLogPrefix, transactionReq.MerchantWithdrawal, dlqName, processorCfg, limiter, ethSender, tronSender)
	case model.TransactionTypeMerchantSettlement:
		processMerchantSettlement(ctx, enhancedLogPrefix, transactionReq.MerchantSettlement, dlqName, processorCfg, limiter, ethSender, tronSender)
	default:
		err := fmt.Errorf("unknown transaction type: %s", transactionReq.Type)
		glog.Errorf(ctx, "%s %v", enhancedLogPrefix, err)
		MoveToDlq(ctx, logPrefix, message, dlqName, err)
	}
}

// processUserWithdrawal handles user withdrawal processing
func processUserWithdrawal(
	ctx context.Context,
	logPrefix string,
	withdrawal *taskv1.Withdrawal,
	dlqName string,
	processorCfg *wp.WithdrawalConfig,
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) {
	glog.Infof(ctx, "%s Processing user withdrawal ID: %d", logPrefix, withdrawal.UserWithdrawsId)
	
	// Process using withdrawal processor steps
	var txHash string
	var errorMessage string
	var finalState int32
	var retries int32 = withdrawal.Retries
	var autoWithdrawalProgress *int32
	
	// Step 1: Derive withdrawal info
	derivedInfo, err := wp.DeriveWithdrawalInfo(ctx, withdrawal)
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to derive withdrawal info: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		finalState = 5 // Failed
		goto updateStatus
	}
	
	// Step 2: Get token configuration
	tokenConfig, err := wp.GetTokenConfiguration(ctx, derivedInfo.TokenKey, processorCfg)
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to get token configuration: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		finalState = 5 // Failed
		goto updateStatus
	}
	
	// Step 3: Validate amount and rate limits
	if !wp.ValidateAmountAndLimits(ctx, derivedInfo, tokenConfig, limiter) {
		errorMessage = "Amount validation or rate limit failed"
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		finalState = 1 // Retry
		goto updateStatus
	}
	
	// Step 4: Select blockchain sender
	var selectedSender wp.BlockchainSender
	switch derivedInfo.Blockchain {
	case "ETH":
		selectedSender = ethSender
	case "TRON":
		selectedSender = tronSender
	default:
		errorMessage = fmt.Sprintf("Unsupported blockchain: %s", derivedInfo.Blockchain)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		finalState = 5 // Failed
		goto updateStatus
	}
	
	// Step 5: Prepare transaction parameters
	txParams, err := wp.PrepareTransactionParams(ctx, derivedInfo, tokenConfig, selectedSender)
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to prepare transaction: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		
		// Check if it's insufficient balance
		if wp.IsInsufficientBalanceError(err) {
			finalState = 2 // Insufficient balance
		} else {
			finalState = 5 // Failed
		}
		goto updateStatus
	}
	
	// Step 6: Send transaction
	txHash, err = wp.SendTransaction(ctx, txParams, selectedSender)
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to send transaction: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		
		// Check if it's insufficient balance
		if wp.IsInsufficientBalanceError(err) {
			finalState = 2 // Insufficient balance
		} else {
			finalState = 5 // Failed
		}
		goto updateStatus
	}
	
	// Success!
	finalState = 4 // Completed
	progress := int32(2) // Set auto_withdrawal_progress to 2 for completed
	autoWithdrawalProgress = &progress
	glog.Infof(ctx, "%s User withdrawal processed successfully. TxHash: %s", logPrefix, txHash)
	
updateStatus:
	// Push user withdrawal status update
	err = wp.PushStatusUpdateToRedisWithProgress(ctx, withdrawal.UserWithdrawsId, finalState, txHash, errorMessage, retries, autoWithdrawalProgress)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to push user withdrawal status update: %v", logPrefix, err)
		MoveToDlq(ctx, logPrefix, fmt.Sprintf("{\"type\":\"user_withdrawal\",\"id\":%d}", withdrawal.UserWithdrawsId), dlqName, err)
	} else {
		glog.Infof(ctx, "%s Successfully pushed user withdrawal status update (ID: %d) with state %d", 
			logPrefix, withdrawal.UserWithdrawsId, finalState)
	}
}

// processMerchantAsUser processes a merchant transaction as a user withdrawal
// but tracks the original transaction type for proper status updates
func processMerchantAsUser(
	ctx context.Context,
	logPrefix string,
	userWithdrawal *taskv1.Withdrawal,
	transactionType string, // "merchant_withdrawal" or "merchant_settlement"
	transactionID int64,    // Original merchant transaction ID
	dlqName string,
	processorCfg *wp.WithdrawalConfig,
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) {
	glog.Infof(ctx, "%s Processing %s (ID: %d) as user withdrawal format", logPrefix, transactionType, transactionID)
	
	// Process using withdrawal processor steps
	var txHash string
	var errorMessage string
	var finalState int32
	var retries int32 = userWithdrawal.Retries
	
	// Step 1: Derive withdrawal info
	derivedInfo, err := wp.DeriveWithdrawalInfo(ctx, userWithdrawal)
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to derive withdrawal info: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		finalState = 5 // Failed
		goto updateStatus
	}
	
	// Step 2: Get token configuration
	tokenConfig, err := wp.GetTokenConfiguration(ctx, derivedInfo.TokenKey, processorCfg)
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to get token configuration: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		finalState = 5 // Failed
		goto updateStatus
	}
	
	// Step 3: Validate amount and rate limits
	if !wp.ValidateAmountAndLimits(ctx, derivedInfo, tokenConfig, limiter) {
		errorMessage = "Amount validation or rate limit failed"
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		finalState = 2 // Keep in processing state for retry
		goto updateStatus
	}
	
	// Step 4: Select blockchain sender
	var selectedSender wp.BlockchainSender
	switch derivedInfo.Blockchain {
	case "ETH":
		selectedSender = ethSender
	case "TRON":
		selectedSender = tronSender
	default:
		errorMessage = fmt.Sprintf("Unsupported blockchain: %s", derivedInfo.Blockchain)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		finalState = 5 // Failed
		goto updateStatus
	}
	
	// Step 5: Prepare transaction parameters
	txParams, err := wp.PrepareTransactionParams(ctx, derivedInfo, tokenConfig, selectedSender)
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to prepare transaction: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		
		// Check if it's insufficient balance
		if wp.IsInsufficientBalanceError(err) {
			finalState = 2 // Keep in processing state for retry
		} else {
			finalState = 5 // Failed
		}
		goto updateStatus
	}
	
	// Step 6: Send transaction
	txHash, err = wp.SendTransaction(ctx, txParams, selectedSender)
	if err != nil {
		errorMessage = fmt.Sprintf("Failed to send transaction: %v", err)
		glog.Errorf(ctx, "%s %s", logPrefix, errorMessage)
		
		// Check if it's insufficient balance
		if wp.IsInsufficientBalanceError(err) {
			finalState = 2 // Keep in processing state for retry
		} else {
			finalState = 5 // Failed
		}
		goto updateStatus
	}
	
	// Success!
	finalState = 4 // Completed
	glog.Infof(ctx, "%s %s processed successfully. TxHash: %s", logPrefix, transactionType, txHash)
	
updateStatus:
	// Push merchant status update instead of user withdrawal status update
	err = merchant_handler.PushMerchantStatusUpdate(ctx, transactionType, transactionID, finalState, txHash, errorMessage, retries)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to push merchant status update: %v", logPrefix, err)
		// Move to DLQ if we can't even push the status update
		MoveToDlq(ctx, logPrefix, fmt.Sprintf("{\"type\":\"%s\",\"id\":%d}", transactionType, transactionID), dlqName, err)
	} else {
		glog.Infof(ctx, "%s Successfully pushed merchant status update for %s (ID: %d) with state %d", 
			logPrefix, transactionType, transactionID, finalState)
	}
}

// processMerchantWithdrawal handles merchant withdrawal processing
func processMerchantWithdrawal(
	ctx context.Context,
	logPrefix string,
	withdrawal *taskv1.MerchantWithdraw,
	dlqName string,
	processorCfg *wp.WithdrawalConfig,
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) {
	glog.Infof(ctx, "%s Processing merchant withdrawal", logPrefix)
	
	// Convert merchant withdrawal to user withdrawal format for processing
	// This allows us to reuse the existing processing logic
	userWithdrawal := convertMerchantWithdrawalToUser(withdrawal)
	
	// Process as user withdrawal but track that this is actually a merchant withdrawal
	processMerchantAsUser(ctx, logPrefix, userWithdrawal, "merchant_withdrawal", withdrawal.WithdrawsId, 
		dlqName, processorCfg, limiter, ethSender, tronSender)
}

// processMerchantSettlement handles merchant settlement processing
func processMerchantSettlement(
	ctx context.Context,
	logPrefix string,
	settlement *taskv1.MerchantSettlement,
	dlqName string,
	processorCfg *wp.WithdrawalConfig,
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) {
	glog.Infof(ctx, "%s Processing merchant settlement", logPrefix)
	
	// Convert merchant settlement to user withdrawal format for processing
	// This allows us to reuse the existing processing logic
	userWithdrawal := convertMerchantSettlementToUser(settlement)
	
	// Process as user withdrawal but track that this is actually a merchant settlement
	processMerchantAsUser(ctx, logPrefix, userWithdrawal, "merchant_settlement", settlement.SettlementsId,
		dlqName, processorCfg, limiter, ethSender, tronSender)
}

// convertMerchantWithdrawalToUser converts a merchant withdrawal to user withdrawal format
func convertMerchantWithdrawalToUser(merchant *taskv1.MerchantWithdraw) *taskv1.Withdrawal {
	return &taskv1.Withdrawal{
		UserWithdrawsId: merchant.WithdrawsId,
		UserId:          merchant.MerchantId,
		TokenId:         merchant.TokenId,
		WalletId:        merchant.WalletId,
		Name:            merchant.Name,
		Chan:            merchant.Chan,
		OrderNo:         merchant.OrderNo,
		Address:         merchant.Address,
		RecipientName:   merchant.RecipientName,
		RecipientAccount: merchant.RecipientAccount,
		Amount:          merchant.Amount,
		HandlingFee:     merchant.HandlingFee,
		ActualAmount:    merchant.ActualAmount,
		// Map merchant state to user withdrawal statuses
		AuditStatus:            mapMerchantStateToAuditStatus(merchant.State),
		AutoWithdrawalProgress: 0, // Merchant withdrawals don't use this field
		ProcessingStatus:       mapMerchantStateToProcessingStatus(merchant.State),
		RefuseReasonZh:         merchant.RefuseReasonZh,
		RefuseReasonEn:         merchant.RefuseReasonEn,
		TxHash:                 merchant.TxHash,
		ErrorMessage:           merchant.ErrorMessage,
		UserRemark:             merchant.UserRemark,
		AdminRemark:            merchant.AdminRemark,
		CreatedAt:              merchant.CreatedAt,
		CheckedAt:              merchant.CheckedAt,
		ProcessingAt:           merchant.ProcessingAt,
		CompletedAt:            merchant.CompletedAt,
		UpdatedAt:              merchant.UpdatedAt,
		Retries:                merchant.Retries,
		NergyState:             merchant.NergyState,
	}
}

// convertMerchantSettlementToUser converts a merchant settlement to user withdrawal format
func convertMerchantSettlementToUser(settlement *taskv1.MerchantSettlement) *taskv1.Withdrawal {
	return &taskv1.Withdrawal{
		UserWithdrawsId: settlement.SettlementsId,
		UserId:          settlement.MerchantId,
		TokenId:         settlement.TokenId,
		WalletId:        settlement.WalletId,
		Name:            settlement.Name,
		Chan:            settlement.Chan,
		OrderNo:         settlement.OrderNo,
		Address:         settlement.Address,
		RecipientName:   settlement.RecipientName,
		RecipientAccount: settlement.RecipientAccount,
		Amount:          settlement.Amount,
		HandlingFee:     settlement.HandlingFee,
		ActualAmount:    settlement.ActualAmount,
		// Map settlement state to user withdrawal statuses
		AuditStatus:            mapMerchantStateToAuditStatus(settlement.State),
		AutoWithdrawalProgress: 0, // Settlements don't use this field
		ProcessingStatus:       mapMerchantStateToProcessingStatus(settlement.State),
		RefuseReasonZh:         settlement.RefuseReasonZh,
		RefuseReasonEn:         settlement.RefuseReasonEn,
		TxHash:                 settlement.TxHash,
		ErrorMessage:           settlement.ErrorMessage,
		UserRemark:             settlement.UserRemark,
		AdminRemark:            settlement.AdminRemark,
		CreatedAt:              settlement.CreatedAt,
		CheckedAt:              settlement.CheckedAt,
		ProcessingAt:           settlement.ProcessingAt,
		CompletedAt:            settlement.CompletedAt,
		UpdatedAt:              settlement.UpdatedAt,
		Retries:                settlement.Retries,
		NergyState:             settlement.NergyState,
	}
}

// mapMerchantStateToAuditStatus maps merchant state to user withdrawal audit status
func mapMerchantStateToAuditStatus(state int32) int32 {
	switch state {
	case 1: // 待审核
		return 2 // 待审核
	case 2: // 处理中
		return 3 // 审核通过
	case 3: // 已拒绝
		return 4 // 审核拒绝
	case 4: // 已完成
		return 3 // 审核通过
	case 5: // 失败
		return 3 // 审核通过 (failed during processing, not audit)
	default:
		return 2 // 默认待审核
	}
}

// mapMerchantStateToProcessingStatus maps merchant state to user withdrawal processing status
func mapMerchantStateToProcessingStatus(state int32) int32 {
	switch state {
	case 1: // 待审核
		return 0 // 未开始处理
	case 2: // 处理中
		return 1 // 自动放币处理中
	case 3: // 已拒绝
		return 5 // 失败
	case 4: // 已完成
		return 4 // 成功
	case 5: // 失败
		return 5 // 失败
	default:
		return 0 // 默认未开始
	}
}

// handleTransactionResult handles the result of transaction processing
// This is a placeholder for the actual result handling logic
func handleTransactionResult(ctx context.Context, logPrefix string, transactionID int64, transactionType string) {
	// This would implement the result handling logic
	// Similar to the existing HandleResult function but adapted for different transaction types
	glog.Infof(ctx, "%s Transaction result handling completed for %s ID %d", logPrefix, transactionType, transactionID)
}

// MoveToDlq moves a message to the dead letter queue (placeholder)
func MoveToDlq(ctx context.Context, logPrefix, message, dlqName string, err error) {
	glog.Errorf(ctx, "%s Moving message to DLQ '%s' due to error: %v. Message: %s", logPrefix, dlqName, err, message)
	// Implementation would move the message to DLQ
}
