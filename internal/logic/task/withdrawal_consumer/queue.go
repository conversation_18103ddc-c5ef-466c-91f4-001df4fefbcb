package withdrawal_consumer

import (
	"context"
	"encoding/json"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"

	"task-withdraw/internal/service"
)

// MoveToDlq attempts to move a problematic message to the Dead-Letter Queue.
func MoveToDlq(ctx context.Context, logPrefix string, message string, dlqName string, originalError error) {
	if dlqName == "" {
		glog.Warningf(ctx, "%s DLQ name is empty, cannot move message. Original error: %v", logPrefix, originalError)
		return
	}

	redisClient := service.Redis().Client()
	dlqMessage := g.Map{
		"original_message": message,
		"error":            originalError.Error(),
		"timestamp":        time.Now().Format(time.RFC3339),
	}
	dlqMessageBytes, err := json.Marshal(dlqMessage)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to marshal DLQ message: %v. Original error: %v", logPrefix, err, originalError)
		return // Cannot proceed if marshalling fails
	}

	_, err = redisClient.LPush(ctx, dlqName, string(dlqMessageBytes))
	if err != nil {
		glog.Errorf(ctx, "%s Failed to LPUSH message to DLQ '%s': %v. Original error: %v", logPrefix, dlqName, err, originalError)
	} else {
		glog.Infof(ctx, "%s Successfully moved message to DLQ '%s'. Original error: %v", logPrefix, dlqName, originalError)
	}
}
