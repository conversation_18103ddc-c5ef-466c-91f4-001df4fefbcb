package withdrawal_consumer

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	taskv1 "task-withdraw/api"
	"task-withdraw/internal/config"
	wp "task-withdraw/internal/logic/task/withdrawal_processor"
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/eth"
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/tron"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
)

// ProcessMessage handles deserialization and processing of a single message.
// It now receives processorCfg instead of consumerCfg.
func ProcessMessage(
	ctx context.Context,
	logPrefix string,
	message string,
	dlqName string,
	processorCfg *wp.WithdrawalConfig, // Changed from consumerCfg
	// Pass initialized dependencies
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) {
	// Add panic recovery for safety
	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic recovered during message processing: %v", r)
			glog.Criticalf(ctx, "%s %v", logPrefix, err)
			// Attempt to move the problematic message to DLQ
			MoveToDlq(ctx, logPrefix, message, dlqName, err)
		}
	}()

	// Check if auto withdrawal is enabled
	autoWithdrawalEnabled, err := config.GetBool(ctx, "auto_withdrawal_setting.state", false)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to get auto_withdrawal_setting.state: %v. Assuming disabled.", logPrefix, err)
		autoWithdrawalEnabled = false
	}

	if !autoWithdrawalEnabled {
		glog.Warningf(ctx, "%s Auto withdrawal is disabled (auto_withdrawal_setting.state=false). Skipping message processing.", logPrefix)
		// Don't move to DLQ, just skip processing
		return
	}

	var withdrawal taskv1.Withdrawal
	err = json.Unmarshal([]byte(message), &withdrawal)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to unmarshal message: %v. Moving to DLQ '%s'. Message: %s", logPrefix, err, dlqName, message)
		MoveToDlq(ctx, logPrefix, message, dlqName, err)
		return
	}

	// Create a more specific log prefix for this withdrawal ID
	recordLogPrefix := fmt.Sprintf("%s[ConsumeWithdrawalID:%d, OrderNo:%s]", logPrefix, withdrawal.UserWithdrawsId, withdrawal.OrderNo)
	glog.Infof(ctx, "%s Start processing withdrawal. Retries so far: %d", recordLogPrefix, withdrawal.Retries) // Log retries from message

	// Declare variables needed across steps or in HandleResult
	var processErr error
	var txHash string
	var selectedSender wp.BlockchainSender      // <<< Use the EXPORTED interface type
	processingStatus := wp.ProcessStatusSkipped // Default to skipped/retry unless success or hard fail
	var isInsufficientBalance bool              // Track if error is due to insufficient balance

	// --- Core Processing Logic ---
	// Use a separate context for the processing steps? Might be overkill unless steps are very long.
	processCtx := ctx // Use worker context for now

	// Use a single-iteration loop with break statements for cleaner error handling instead of goto
	for { // Use a single-iteration loop to allow breaking out on error
		// Step 1: Derive Info
		symbol, chainType, tokenStandard, tokenConfigKey, err := wp.DeriveWithdrawalInfo(&withdrawal) // Pass pointer
		if err != nil {
			glog.Errorf(ctx, "%s Failed to derive withdrawal info: %v", recordLogPrefix, err)
			processErr = err                          // Mark as error
			processingStatus = wp.ProcessStatusFailed // Non-retryable derivation issue
			break                                     // Exit loop
		}
		symbolUpper := gstr.ToUpper(symbol)
		tokenStandardUpper := gstr.ToUpper(tokenStandard)
		glog.Debugf(ctx, "%s Derived Info: Symbol=%s, Chain=%s, Standard=%s, ConfigKey=%s", recordLogPrefix, symbol, chainType, tokenStandard, tokenConfigKey)

		// Step 2: NergyState Check (TRC20 only)
		// skip, err := wp.CheckNergyState(processCtx, &withdrawal, tokenStandardUpper, recordLogPrefix)
		// if err != nil {
		// 	glog.Errorf(ctx, "%s Error during NergyState check: %v", recordLogPrefix, err)
		// 	processErr = err
		// 	processingStatus = wp.ProcessStatusFailed // Assume non-retryable
		// 	break                                     // Exit loop
		// }
		// if skip {
		// 	glog.Infof(ctx, "%s Skipped due to NergyState check.", recordLogPrefix)
		// 	// processingStatus remains Skipped, processErr is nil
		// 	break // Exit loop
		// }

		// Step 3: Get Token Configuration
		// Use processorCfg directly as it's already a pointer
		contractAddress, precision, err := wp.GetTokenConfig(processCtx, processorCfg, tokenConfigKey, tokenStandardUpper, recordLogPrefix)
		if err != nil {
			glog.Errorf(ctx, "%s Failed to get token config: %v", recordLogPrefix, err) // Error logged within GetTokenConfig
			processErr = err
			processingStatus = wp.ProcessStatusFailed // Non-retryable config issue
			break                                     // Exit loop
		}

		// Step 4: Check if Token Withdrawal is Enabled
		// Use processorCfg directly
		skip, err := wp.CheckTokenEnabled(processCtx, processorCfg, tokenConfigKey, symbolUpper, tokenStandardUpper, recordLogPrefix)
		if err != nil {
			glog.Errorf(ctx, "%s Error during token enabled check: %v", recordLogPrefix, err)
			processErr = err
			processingStatus = wp.ProcessStatusFailed // Assume non-retryable
			break                                     // Exit loop
		}
		if skip {
			glog.Infof(ctx, "%s Skipped because token withdrawal is disabled.", recordLogPrefix)
			// processingStatus remains Skipped, processErr is nil
			break // Exit loop
		}

		// Step 5: Validate Recipient Address
		recipientAddress := withdrawal.Address
		err = wp.ValidateAddress(processCtx, recipientAddress, chainType, recordLogPrefix)
		if err != nil {
			glog.Errorf(ctx, "%s Invalid recipient address: %v", recordLogPrefix, err) // Error logged within ValidateAddress
			processErr = err
			processingStatus = wp.ProcessStatusFailed // Non-retryable format/chain issue
			break                                     // Exit loop
		}

		// Step 6: Validate Amount and Check Single Limit
		amountDecimal, skip, err := wp.ValidateAmountAndLimit(processCtx, &withdrawal, limiter, symbol, recordLogPrefix)
		if err != nil {
			glog.Errorf(ctx, "%s Amount validation or limit check failed: %v", recordLogPrefix, err) // Error logged within func
			processErr = err
			// Let HandleResult decide if it's retryable based on error type (e.g., limit exceeded might be temporary)
			processingStatus = wp.ProcessStatusFailed // Default to failed, HandleResult might change it
			break                                     // Exit loop
		}
		if skip {
			glog.Infof(ctx, "%s Skipped due to amount/limit check.", recordLogPrefix)
			// processingStatus remains Skipped, processErr is nil
			break // Exit loop
		}

		// Step 7: Select Sender and Check Hot Wallet Balance
		selectedSender, err = wp.SelectSenderAndCheckBalance( // Assign to pre-declared variable
			processCtx, chainType, ethSender, tronSender, symbol, precision, contractAddress, tokenStandard, amountDecimal, recordLogPrefix,
		)
		if err != nil {
			glog.Errorf(ctx, "%s Failed to select sender or check balance: %v", recordLogPrefix, err) // Error logged within func
			processErr = err
			// Let HandleResult decide retry based on error type (e.g., balance low is retryable)
			processingStatus = wp.ProcessStatusFailed // Default to failed
			break                                     // Exit loop
		}

		// Step 7.5: Check and Purchase Energy for TRC20 (if needed)
		// 获取资金池地址 - 从配置获取
		poolAddress := ""
		if tronSender != nil && chainType == "TRON" {
			// 从配置获取TRON钱包地址
			if processorCfg != nil {
				if tronWalletCfg, ok := processorCfg.Wallets["TRON"]; ok {
					poolAddress = tronWalletCfg.Address
				}
			}
			if poolAddress == "" {
				glog.Errorf(ctx, "%s Failed to get TRON wallet address from config", recordLogPrefix)
				processErr = gerror.New("TRON wallet address not configured")
				processingStatus = wp.ProcessStatusFailed
				break
			}
		}

		if poolAddress != "" {
			err = wp.CheckAndPurchaseEnergyForTRC20(processCtx, &withdrawal, tokenStandard, poolAddress, recordLogPrefix)
			if err != nil {
				glog.Infof(ctx, "%s Energy check result: %v", recordLogPrefix, err) // 这可能是正常的等待状态

				// 检查是否是能量购买/等待相关的错误
				errMsg := err.Error()
				if strings.Contains(errMsg, "energy purchase initiated") ||
					strings.Contains(errMsg, "waiting for energy order completion") ||
					strings.Contains(errMsg, "energy order") {
					// 这是能量购买/等待状态，不是真正的错误
					glog.Infof(ctx, "%s Energy management in progress, will retry later.", recordLogPrefix)
					processErr = err
					processingStatus = wp.ProcessStatusSkipped // 标记为跳过，稍后重试
					break                                      // Exit loop
				} else {
					// 其他能量相关错误
					glog.Errorf(ctx, "%s Energy check failed: %v", recordLogPrefix, err)
					processErr = err
					processingStatus = wp.ProcessStatusFailed
					break // Exit loop
				}
			}
		}

		// Step 8: Send Transaction
		txHash, err = wp.SendTransaction( // Assign to pre-declared variable
			processCtx, selectedSender, recipientAddress, amountDecimal, symbol, tokenStandard, contractAddress, precision, recordLogPrefix,
		)
		if err != nil {
			glog.Errorf(ctx, "%s Failed to send transaction: %v", recordLogPrefix, err) // Error logged within func
			processErr = err
			processingStatus = wp.ProcessStatusFailed // Usually retryable, HandleResult will confirm
			break                                     // Exit loop
		}

		// If we reach here, the transaction was sent successfully (or at least submitted)
		glog.Infof(ctx, "%s Transaction sent successfully. TxHash: %s", recordLogPrefix, txHash)
		processingStatus = wp.ProcessStatusSuccess

		break // Exit the loop after successful processing or error handling within steps
	}

	// --- Handle Processing Result ---
	finalState := int32(0) // Default invalid state
	errorMessageJson := ""
	retries := withdrawal.Retries // Keep current retry count unless incrementing

	if processingStatus == wp.ProcessStatusSuccess {
		finalState = 4 // State 4: Processing/Completed by consumer
		// txHash is already set
		// Also set auto_withdrawal_progress to 2 (completed) for successful transactions
	} else {
		// Handle errors (Failed or Skipped)
		// Use adapted logic from wp.handleProcessingError to decide final state and message
		isNonRetryable := false
		if processErr != nil { // Only check non-retryable if there was an actual error
			errMsgLower := gstr.ToLower(processErr.Error())
			// Check if this is an insufficient balance error
			if gstr.Contains(errMsgLower, "insufficient") && gstr.Contains(errMsgLower, "balance") {
				isInsufficientBalance = true
				glog.Warningf(ctx, "%s Insufficient balance detected: %s", recordLogPrefix, processErr.Error())
			}

			// Access Retry config from processorCfg
			if processorCfg != nil { // Check if processorCfg is not nil
				for _, nonRetryableSubstr := range processorCfg.Retry.NonRetryableErrors {
					if nonRetryableSubstr != "" && gstr.Contains(errMsgLower, gstr.ToLower(nonRetryableSubstr)) {
						isNonRetryable = true
						glog.Warningf(ctx, "%s Error '%s' contains non-retryable keyword '%s'. Marking as failed.", recordLogPrefix, processErr.Error(), nonRetryableSubstr)
						break
					}
				}
			} else {
				glog.Warningf(ctx, "%s processorCfg is nil, cannot check non-retryable errors.", recordLogPrefix)
				// Decide default behavior: treat as retryable or non-retryable? Assume retryable for now.
			}
		} else if processingStatus == wp.ProcessStatusSkipped {
			// If status is skipped but no error, it was an intentional skip (Nergy, Disabled, Limit etc.)
			// We don't need to push an update or retry in this case. Log and return.
			glog.Infof(ctx, "%s Processing skipped intentionally (Nergy/Disabled/Limit). No status update pushed.", recordLogPrefix)
			return // Exit processing for this message
		}

		// Access Retry config from processorCfg
		maxAttempts := 3 // Default
		if processorCfg != nil {
			if processorCfg.Retry.Maxattempts > 0 {
				maxAttempts = processorCfg.Retry.Maxattempts
			} else {
				glog.Warningf(ctx, "%s processorCfg.Retry.Maxattempts is not positive (%d), using default: %d", recordLogPrefix, processorCfg.Retry.Maxattempts, maxAttempts)
			}
		} else {
			glog.Warningf(ctx, "%s processorCfg is nil, using default max attempts: %d", recordLogPrefix, maxAttempts)
		}

		// Prepare error message JSON
		var currentErrors *gjson.Json
		if withdrawal.ErrorMessage != "" {
			parsedJson, errJson := gjson.LoadJson([]byte(withdrawal.ErrorMessage))

			// 检查是否是数组
			isArray := false
			if errJson == nil {
				// 尝试通过解析结构来判断是否是数组
				var testArr []interface{}
				if err := json.Unmarshal([]byte(withdrawal.ErrorMessage), &testArr); err == nil {
					isArray = true
				}
			}

			if errJson == nil && isArray {
				currentErrors = parsedJson
			} else {
				// Log warning if parsing failed or not an array
				if errJson != nil {
					glog.Warningf(ctx, "%s Failed to parse existing ErrorMessage JSON ('%s'): %v. Initializing new.", recordLogPrefix, withdrawal.ErrorMessage, errJson)
				} else {
					glog.Warningf(ctx, "%s Existing ErrorMessage ('%s') is not a JSON array. Initializing new.", recordLogPrefix, withdrawal.ErrorMessage)
				}
				currentErrors = gjson.New("[]") // Initialize if parse fails or not array
			}
		} else {
			currentErrors = gjson.New("[]")
		}

		newErrorEntry := g.Map{
			"timestamp": time.Now().Format(time.RFC3339),
			"error":     "Unknown error", // Default
		}
		if processErr != nil {
			newErrorEntry["error"] = processErr.Error()
		}

		if isInsufficientBalance {
			// Special handling for insufficient balance: set processing_status=2, auto_withdrawal_progress=0
			finalState = 2 // State 2: Processing (待冷钱包转入)
			glog.Warningf(ctx, "%s Insufficient balance detected. Setting processing_status=2, auto_withdrawal_progress=0. Error: %v", recordLogPrefix, processErr)

			newErrorEntry["insufficient_balance"] = true
			newErrorEntry["final"] = true
			_ = currentErrors.Append(".", newErrorEntry)
			errorMessageJson = currentErrors.MustToJsonString()

			// Remove from local queue (don't move to DLQ, just process and update status)
			// The message will be consumed and not requeued
		} else if !isNonRetryable && processErr != nil && withdrawal.Retries < int32(maxAttempts) {
			// Retryable error and attempts remaining
			finalState = 1 // State 1: Pending (for retry)
			retries = withdrawal.Retries + 1
			newErrorEntry["retry"] = retries
			glog.Infof(ctx, "%s Scheduling retry %d/%d for error: %v", recordLogPrefix, retries, maxAttempts, processErr)
			_ = currentErrors.Append(".", newErrorEntry) // Append error, ignore append error for simplicity here
			errorMessageJson = currentErrors.MustToJsonString()
		} else {
			// Non-retryable error, max retries reached, or other failure
			finalState = 5 // State 5: Failed
			failureReason := "Unknown"
			if processErr == nil {
				failureReason = "Reached HandleResult with Failed status but no error" // Should not happen
			} else if isNonRetryable {
				failureReason = "Non-retryable error"
			} else if withdrawal.Retries >= int32(maxAttempts) {
				failureReason = "Max retries reached"
			}
			glog.Errorf(ctx, "%s Marking withdrawal as permanently failed. Reason: %s. Error: %v", recordLogPrefix, failureReason, processErr)

			newErrorEntry["final"] = true
			_ = currentErrors.Append(".", newErrorEntry)
			errorMessageJson = currentErrors.MustToJsonString()

			// Move original message to DLQ for permanent failures
			if processErr != nil { // Only move to DLQ if there was a processing error leading to failure
				MoveToDlq(ctx, recordLogPrefix, message, dlqName, processErr)
			}
		}
	}

	// Push the final status update to the status queue
	if finalState != 0 { // Only push if a valid final state was determined
		var err error
		if isInsufficientBalance && finalState == 2 {
			// For insufficient balance, set auto_withdrawal_progress=0
			autoWithdrawalProgress := int32(0)
			err = wp.PushStatusUpdateToRedisWithProgress(ctx, withdrawal.UserWithdrawsId, finalState, txHash, errorMessageJson, retries, &autoWithdrawalProgress)
			glog.Infof(ctx, "%s Pushing status update for insufficient balance: processing_status=%d, auto_withdrawal_progress=0", recordLogPrefix, finalState)
		} else if finalState == 4 {
			// For successful transactions, set auto_withdrawal_progress=2 (completed)
			autoWithdrawalProgress := int32(2)
			err = wp.PushStatusUpdateToRedisWithProgress(ctx, withdrawal.UserWithdrawsId, finalState, txHash, errorMessageJson, retries, &autoWithdrawalProgress)
			glog.Infof(ctx, "%s Pushing status update for success: processing_status=%d, auto_withdrawal_progress=2", recordLogPrefix, finalState)
		} else {
			err = wp.PushStatusUpdateToRedis(ctx, withdrawal.UserWithdrawsId, finalState, txHash, errorMessageJson, retries)
		}

		if err != nil {
			glog.Errorf(ctx, "%s CRITICAL: Failed to push final status update (State: %d) to Redis queue '%s': %v", recordLogPrefix, finalState, "queue:withdrawal_status_update", err)
			// What to do now? The task is processed but status update failed.
			// Options:
			// 1. Retry pushing the status update indefinitely?
			// 2. Move the *original* message (or the status update message) to a separate DLQ for manual intervention?
			// For now, just log critical error. Consider adding DLQ logic for status push failures later.
		} else {
			glog.Infof(ctx, "%s Successfully pushed final status update (State: %d) to Redis.", recordLogPrefix, finalState)
		}
	} else if processingStatus != wp.ProcessStatusSkipped {
		// Should not happen unless logic error above
		glog.Errorf(ctx, "%s Reached end of processing without determining a final state and not intentionally skipped.", recordLogPrefix)
	}
}
