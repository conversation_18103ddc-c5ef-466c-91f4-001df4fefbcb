package task

import (
	"context"
	"task-withdraw/internal/task_registry" // 新增导入

	"github.com/gogf/gf/v2/frame/g" // 导入 g 包

	enhanced_processor "task-withdraw/internal/logic/task/enhanced_processor" // Import enhanced processor
)

func init() {

	// 表达式示例	表达式说明
	// * * * * * *	每秒执行
	// # * * * * *	每分钟执行，每一次执行至少间隔 60 秒
	// 2 * * * * *	每分钟的第 2 秒执行
	// */5 * * * * *	每 5 秒执行一次
	// # */30 * * * *	每 30 分钟执行一次
	// # 0 2 * * *	每天凌晨 2 点执行
	// # */30 9-18 * * *	每天 9 点到 18 点，每隔 30 分钟执行一次
	// # 0 9 * * MON,FRI	每 周一 和 周五 在 9 点执行一次

	// Register Enhanced Transaction Processor Task (includes user withdrawals, merchant withdrawals, and settlements)
	task_registry.Register(task_registry.TaskInfo{
		Name: "EnhancedTransactionProcessorTask",
		SpecFunc: func(ctx context.Context) (spec string, enabled bool, err error) {
			// Use the config key defined in withdrawal_processor/config.go
			configPrefix := "withdrawalProcessor"
			spec = g.Cfg().MustGet(ctx, configPrefix+".spec", "").String()        // Default empty spec if not found
			enabled = g.Cfg().MustGet(ctx, configPrefix+".enabled", false).Bool() // Default disabled

			// Use g.Log() which is available via "github.com/gogf/gf/v2/frame/g"
			if enabled && spec == "" {
				g.Log().Warningf(ctx, "Task '%s' is enabled but spec is empty or missing in config ('%s.spec'). Task will not run.", "EnhancedTransactionProcessorTask", configPrefix)
				// Optionally disable it if spec is crucial
				// enabled = false
			}
			return spec, enabled, nil
		},
		Func:        enhanced_processor.ProcessPendingTransactions, // Point to the enhanced processor function
		IsSingleton: true,                                         // Ensure only one instance runs at a time
	})

	// Register Energy Order Checker (Background Service)
	// This is not a scheduled task but a continuous background service
	// It will be started by the main application
}
