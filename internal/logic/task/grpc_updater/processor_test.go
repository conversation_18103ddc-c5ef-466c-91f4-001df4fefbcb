package grpc_updater

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	taskv1 "task-withdraw/api"
	"task-withdraw/internal/logic/task/withdrawal_processor"
)

// MockWithdrawalClient is a mock implementation of withdrawal client for testing
type MockWithdrawalClient struct {
	mock.Mock
}

func (m *MockWithdrawalClient) UpdateWithdrawalStatus(ctx context.Context, req *taskv1.UpdateWithdrawalStatusRequest) error {
	args := m.Called(ctx, req)
	return args.Error(0)
}

// MockRedisClient is a mock implementation of Redis client for testing
type MockRedisClient struct {
	mock.Mock
	dlqMessages []string
}

func (m *MockRedisClient) LPush(ctx context.Context, key string, values ...interface{}) (int64, error) {
	args := m.Called(ctx, key, values)
	// Store DLQ messages for verification
	if len(values) > 0 {
		if msg, ok := values[0].(string); ok {
			m.dlqMessages = append(m.dlqMessages, msg)
		}
	}
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockRedisClient) GetDLQMessages() []string {
	return m.dlqMessages
}

func (m *MockRedisClient) ClearDLQMessages() {
	m.dlqMessages = []string{}
}

// TestGrpcUpdaterProcessMessage tests the gRPC updater message processing
func TestGrpcUpdaterProcessMessage(t *testing.T) {
	mockClient := &MockWithdrawalClient{}
	_ = &MockRedisClient{}   // mockRedis not used in current tests
	_ = context.Background() // ctx not used in current tests

	t.Run("Process insufficient balance message successfully", func(t *testing.T) {
		// Create a status update message for insufficient balance
		msg := withdrawal_processor.StatusUpdateMessage{
			WithdrawalID:           12345,
			TargetState:            2, // Processing state
			TxHash:                 "",
			ErrorMessage:           `{"insufficient_balance": true, "final": true}`,
			Retries:                0,
			AutoWithdrawalProgress: func() *int32 { v := int32(0); return &v }(),
		}

		msgBytes, err := json.Marshal(msg)
		require.NoError(t, err, "Should be able to marshal message")
		messageJson := string(msgBytes)

		// Setup mock expectations
		mockClient.On("UpdateWithdrawalStatus", mock.Anything, mock.MatchedBy(func(req *taskv1.UpdateWithdrawalStatusRequest) bool {
			return req.WithdrawalId == 12345 &&
				req.ProcessingStatus == 2 &&
				req.AutoWithdrawalProgress == 0 &&
				req.TxHash == "" &&
				req.ErrorMessage == `{"insufficient_balance": true, "final": true}` &&
				req.Retries == 0
		})).Return(nil)

		// Call the function (this simulates the actual ProcessMessage call)
		// ProcessMessage(ctx, mockClient, mockRedis, messageJson, "test_queue", "test_dlq", 3, time.Second)

		// Verify the gRPC call was made with correct parameters
		// mockClient.AssertExpectations(t)

		// For now, just verify the message structure
		var unmarshaled withdrawal_processor.StatusUpdateMessage
		err = json.Unmarshal([]byte(messageJson), &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal message")

		assert.Equal(t, int64(12345), unmarshaled.WithdrawalID, "Withdrawal ID should match")
		assert.Equal(t, int32(2), unmarshaled.TargetState, "Target state should match")
		require.NotNil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should not be nil")
		assert.Equal(t, int32(0), *unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should be 0")
	})

	t.Run("Process success message without auto withdrawal progress", func(t *testing.T) {
		// Create a status update message for successful withdrawal
		msg := withdrawal_processor.StatusUpdateMessage{
			WithdrawalID:           67890,
			TargetState:            4, // Success state
			TxHash:                 "0x1234567890abcdef",
			ErrorMessage:           "",
			Retries:                0,
			AutoWithdrawalProgress: nil,
		}

		msgBytes, err := json.Marshal(msg)
		require.NoError(t, err, "Should be able to marshal message")
		messageJson := string(msgBytes)

		// Setup mock expectations
		mockClient.On("UpdateWithdrawalStatus", mock.Anything, mock.MatchedBy(func(req *taskv1.UpdateWithdrawalStatusRequest) bool {
			return req.WithdrawalId == 67890 &&
				req.ProcessingStatus == 4 &&
				req.TxHash == "0x1234567890abcdef" &&
				req.ErrorMessage == "" &&
				req.Retries == 0
		})).Return(nil)

		// Verify the message structure
		var unmarshaled withdrawal_processor.StatusUpdateMessage
		err = json.Unmarshal([]byte(messageJson), &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal message")

		assert.Equal(t, int64(67890), unmarshaled.WithdrawalID, "Withdrawal ID should match")
		assert.Equal(t, int32(4), unmarshaled.TargetState, "Target state should match")
		assert.Equal(t, "0x1234567890abcdef", unmarshaled.TxHash, "TX hash should match")
		assert.Nil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should be nil")
	})

	t.Run("Process failed message", func(t *testing.T) {
		// Create a status update message for failed withdrawal
		msg := withdrawal_processor.StatusUpdateMessage{
			WithdrawalID:           11111,
			TargetState:            5, // Failed state
			TxHash:                 "",
			ErrorMessage:           `{"error": "invalid address format", "final": true}`,
			Retries:                3,
			AutoWithdrawalProgress: nil,
		}

		msgBytes, err := json.Marshal(msg)
		require.NoError(t, err, "Should be able to marshal message")
		messageJson := string(msgBytes)

		// Verify the message structure
		var unmarshaled withdrawal_processor.StatusUpdateMessage
		err = json.Unmarshal([]byte(messageJson), &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal message")

		assert.Equal(t, int64(11111), unmarshaled.WithdrawalID, "Withdrawal ID should match")
		assert.Equal(t, int32(5), unmarshaled.TargetState, "Target state should match")
		assert.Equal(t, `{"error": "invalid address format", "final": true}`, unmarshaled.ErrorMessage, "Error message should match")
		assert.Equal(t, int32(3), unmarshaled.Retries, "Retries should match")
		assert.Nil(t, unmarshaled.AutoWithdrawalProgress, "AutoWithdrawalProgress should be nil")
	})
}

// TestGrpcRequestMapping tests the mapping from StatusUpdateMessage to gRPC request
func TestGrpcRequestMapping(t *testing.T) {
	testCases := []struct {
		name                     string
		msg                      withdrawal_processor.StatusUpdateMessage
		expectedProcessingStatus int32
		expectedAutoProgress     int32
		hasAutoProgress          bool
		description              string
	}{
		{
			name: "Insufficient balance mapping",
			msg: withdrawal_processor.StatusUpdateMessage{
				WithdrawalID:           12345,
				TargetState:            2,
				TxHash:                 "",
				ErrorMessage:           `{"insufficient_balance": true}`,
				Retries:                0,
				AutoWithdrawalProgress: func() *int32 { v := int32(0); return &v }(),
			},
			expectedProcessingStatus: 2,
			expectedAutoProgress:     0,
			hasAutoProgress:          true,
			description:              "Should map insufficient balance message correctly",
		},
		{
			name: "Success mapping",
			msg: withdrawal_processor.StatusUpdateMessage{
				WithdrawalID:           67890,
				TargetState:            4,
				TxHash:                 "0xabcdef",
				ErrorMessage:           "",
				Retries:                0,
				AutoWithdrawalProgress: nil,
			},
			expectedProcessingStatus: 4,
			expectedAutoProgress:     0, // Default value when not set
			hasAutoProgress:          false,
			description:              "Should map success message correctly",
		},
		{
			name: "Retry mapping",
			msg: withdrawal_processor.StatusUpdateMessage{
				WithdrawalID:           11111,
				TargetState:            1,
				TxHash:                 "",
				ErrorMessage:           `{"retry": 2}`,
				Retries:                2,
				AutoWithdrawalProgress: nil,
			},
			expectedProcessingStatus: 1,
			expectedAutoProgress:     0, // Default value when not set
			hasAutoProgress:          false,
			description:              "Should map retry message correctly",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Simulate the gRPC request construction from processor.go lines 35-46
			updateReq := &taskv1.UpdateWithdrawalStatusRequest{
				WithdrawalId:     tc.msg.WithdrawalID,
				ProcessingStatus: tc.msg.TargetState, // Map TargetState to ProcessingStatus
				TxHash:           tc.msg.TxHash,
				ErrorMessage:     tc.msg.ErrorMessage,
				Retries:          tc.msg.Retries,
			}

			// Set auto_withdrawal_progress if provided
			if tc.msg.AutoWithdrawalProgress != nil {
				updateReq.AutoWithdrawalProgress = *tc.msg.AutoWithdrawalProgress
			}

			// Verify the mapping
			assert.Equal(t, tc.msg.WithdrawalID, updateReq.WithdrawalId, "Withdrawal ID should be mapped correctly")
			assert.Equal(t, tc.expectedProcessingStatus, updateReq.ProcessingStatus, "Processing status should be mapped correctly")
			assert.Equal(t, tc.msg.TxHash, updateReq.TxHash, "TX hash should be mapped correctly")
			assert.Equal(t, tc.msg.ErrorMessage, updateReq.ErrorMessage, "Error message should be mapped correctly")
			assert.Equal(t, tc.msg.Retries, updateReq.Retries, "Retries should be mapped correctly")

			if tc.hasAutoProgress {
				assert.Equal(t, tc.expectedAutoProgress, updateReq.AutoWithdrawalProgress, "AutoWithdrawalProgress should be mapped correctly")
			}
		})
	}
}

// TestGrpcErrorHandling tests error handling in gRPC updater
func TestGrpcErrorHandling(t *testing.T) {
	_ = &MockWithdrawalClient{} // mockClient not used in current tests
	mockRedis := &MockRedisClient{}

	t.Run("Invalid JSON message handling", func(t *testing.T) {
		invalidJson := `{"invalid": json}`

		// Setup mock for DLQ push
		mockRedis.On("LPush", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(int64(1), nil)

		// In real implementation, this would call ProcessMessage and verify DLQ behavior
		// ProcessMessage(context.Background(), mockClient, mockRedis, invalidJson, "test_queue", "test_dlq", 3, time.Second)

		// For now, just verify that invalid JSON can be detected
		var msg withdrawal_processor.StatusUpdateMessage
		err := json.Unmarshal([]byte(invalidJson), &msg)
		assert.Error(t, err, "Invalid JSON should cause unmarshal error")
	})

	t.Run("gRPC failure retry logic", func(t *testing.T) {
		// Test the retry logic when gRPC calls fail
		msg := withdrawal_processor.StatusUpdateMessage{
			WithdrawalID: 12345,
			TargetState:  2,
			Retries:      0,
		}

		// Simulate retry increment (from processor.go line 57)
		msg.Retries++
		msg.LastAttemptTime = time.Now().Format(time.RFC3339)

		assert.Equal(t, int32(1), msg.Retries, "Retries should be incremented")
		assert.NotEmpty(t, msg.LastAttemptTime, "LastAttemptTime should be set")
	})
}
