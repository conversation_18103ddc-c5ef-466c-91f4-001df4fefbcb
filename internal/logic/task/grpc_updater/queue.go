package grpc_updater

import (
	"context"

	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/os/glog"
)

// MoveToDLQ attempts to move a message to the Dead Letter Queue.
func MoveToDLQ(ctx context.Context, redisClient *gredis.Redis, dlqName, messageJson, reason string) {
	logPrefix := "[GrpcStatusUpdater.moveToDLQ]"
	_, err := redisClient.LPush(ctx, dlqName, messageJson)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to move message to DLQ '%s' (Reason: %s): %v. Message: %s", logPrefix, dlqName, reason, err, messageJson)
		// Consider adding alerting here for DLQ failures
	} else {
		glog.Warningf(ctx, "%s Message moved to DLQ '%s'. Reason: %s. Message: %s", logPrefix, dlqName, reason, messageJson)
	}
}
