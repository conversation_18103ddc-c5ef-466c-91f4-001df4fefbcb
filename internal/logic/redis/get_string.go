package redis

import (
	"context"
)

// GetString 获取缓存并转换为 string。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) GetString(ctx context.Context, key string, name ...string) (string, error) {
	v, err := s.Get(ctx, key, name...)
	if err != nil {
		return "", err
	}
	if v == nil || v.Is<PERSON>il() {
		return "", nil // Key not found or value is nil
	}
	return v.String(), nil
}
