package redis

import (
	"context"

	"github.com/a19ba14d/tg-bot-common/consts"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// SetUserLanguage 设置用户语言缓存。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) SetUserLanguage(ctx context.Context, telegramID int64, language string, name ...string) error {
	key := consts.UserLanguageKeyPrefix + gconv.String(telegramID)
	stdLang := language // Assuming language is already standardized or handled elsewhere if needed

	g.Log().Debugf(ctx, "Setting user language cache: key=%s, language=%s", key, stdLang)
	// Use the Set method of this service, duration 0 means no expiration
	return s.Set(ctx, key, stdLang, 0, name...)
}
