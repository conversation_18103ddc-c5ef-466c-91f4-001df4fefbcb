package service

import (
	"context"
	taskv1 "task-withdraw/api" // Assuming correct path to generated protobuf types
)

// IWithdrawalClient defines the interface for the withdrawal gRPC client service.
type IWithdrawalClient interface {
	// === User Withdrawal Methods ===
	// FetchPendingWithdrawals fetches pending user withdrawal orders from the gRPC service.
	FetchPendingWithdrawals(ctx context.Context, batchSize int) ([]*taskv1.Withdrawal, error)
	// FetchPendingWithdrawalsWithStatus fetches user withdrawal orders with specific status from the gRPC service.
	FetchPendingWithdrawalsWithStatus(ctx context.Context, batchSize int, processingStatus int32) ([]*taskv1.Withdrawal, error)
	// UpdateWithdrawalStatus updates the status of a user withdrawal order via gRPC.
	UpdateWithdrawalStatus(ctx context.Context, req *taskv1.UpdateWithdrawalStatusRequest) error

	// === Merchant Withdrawal Methods ===
	// FetchPendingMerchantWithdrawals fetches pending merchant withdrawal orders from the gRPC service.
	FetchPendingMerchantWithdrawals(ctx context.Context, batchSize int) ([]*taskv1.MerchantWithdraw, error)
	// UpdateMerchantWithdrawStatus updates the status of a merchant withdrawal order via gRPC.
	UpdateMerchantWithdrawStatus(ctx context.Context, req *taskv1.UpdateMerchantWithdrawRequest) error

	// === Merchant Settlement Methods ===
	// FetchPendingMerchantSettlements fetches pending merchant settlement orders from the gRPC service.
	FetchPendingMerchantSettlements(ctx context.Context, batchSize int) ([]*taskv1.MerchantSettlement, error)
	// UpdateMerchantSettlementStatus updates the status of a merchant settlement order via gRPC.
	UpdateMerchantSettlementStatus(ctx context.Context, req *taskv1.UpdateMerchantSettlementRequest) error

	// === Common Methods ===
	// Close closes the underlying gRPC connection.
	Close() error
}

var (
	// localWithdrawalClient holds the registered implementation.
	localWithdrawalClient IWithdrawalClient
)

// RegisterWithdrawalClient registers the withdrawal client service implementation.
// It is usually called in the init function of the implementing package.
func RegisterWithdrawalClient(i IWithdrawalClient) {
	localWithdrawalClient = i
}

// WithdrawalClient returns the registered withdrawal client service implementation.
// It panics if no implementation is registered.
func WithdrawalClient() IWithdrawalClient {
	if localWithdrawalClient == nil {
		panic("implement not found for interface IWithdrawalClient, forgot register?")
	}
	return localWithdrawalClient
}
