package service

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gredis"
	// "github.com/gogf/gf/v2/errors/gerror" // Uncomment if needed for error handling in Redis()
	// "github.com/gogf/gf/v2/frame/g"      // Uncomment if needed for logging in Redis()
)

// IRedis defines the interface for redis service operations.
type IRedis interface {
	// Client 获取指定名称的 Redis 客户端实例，如果未指定名称则获取默认实例。
	Client(name ...string) *gredis.Redis
	// Set 设置缓存，duration 为 0 表示永不过期。
	Set(ctx context.Context, key string, value interface{}, duration time.Duration, name ...string) error
	// Get 获取缓存。
	Get(ctx context.Context, key string, name ...string) (*gvar.Var, error)
	// GetString 获取缓存并转换为 string。
	GetString(ctx context.Context, key string, name ...string) (string, error)
	// Remove 删除缓存。
	Remove(ctx context.Context, key string, name ...string) error
	// Exists 检查 Key 是否存在。
	Exists(ctx context.Context, key string, name ...string) (bool, error)
	// Incr 自增。
	Incr(ctx context.Context, key string, name ...string) (int64, error)
	// Decr 自减。
	Decr(ctx context.Context, key string, name ...string) (int64, error)
	// LPush 列表左侧推入。
	LPush(ctx context.Context, key string, values ...interface{}) (int64, error)
	// RPop 列表右侧弹出。
	RPop(ctx context.Context, key string) (*gvar.Var, error)
	// HSet 哈希表设置字段。
	HSet(ctx context.Context, key string, field string, value interface{}) (int64, error)
	// HGet 哈希表获取字段。
	HGet(ctx context.Context, key string, field string) (*gvar.Var, error)
	// HGetAll 哈希表获取所有字段。
	HGetAll(ctx context.Context, key string) (*gvar.Var, error)
	// ClearCache 清除内部缓存的客户端实例。
	ClearCache()
	// IncrementPaymentPasswordAttempts 增加用户支付密码尝试次数。
	IncrementPaymentPasswordAttempts(ctx context.Context, userID uint64) (int64, error)
	// GetPaymentPasswordAttempts 获取用户支付密码尝试次数。
	GetPaymentPasswordAttempts(ctx context.Context, userID uint64) (int64, error)
	// ResetPaymentPasswordAttempts 重置用户支付密码尝试次数和锁定状态。
	ResetPaymentPasswordAttempts(ctx context.Context, userID uint64) error
	// LockPaymentPassword 锁定用户支付密码指定时间。
	LockPaymentPassword(ctx context.Context, userID uint64, duration time.Duration) error
	// IsPaymentPasswordLocked 检查用户支付密码是否被锁定。
	IsPaymentPasswordLocked(ctx context.Context, userID uint64) (bool, error)
	// SetUserLanguage 设置用户语言缓存。
	SetUserLanguage(ctx context.Context, telegramID int64, language string, name ...string) error
	// GetUserLanguage 获取用户语言缓存。
	GetUserLanguage(ctx context.Context, telegramID int64, name ...string) (string, error)
}

var (
	localRedis IRedis // localRedis holds the registered Redis service implementation.
)

// RegisterRedis registers the Redis service implementation.
// It is usually called in the init function of the implementing package.
func RegisterRedis(i IRedis) {
	localRedis = i
}

// Redis returns the registered Redis service implementation.
// It panics if no implementation is registered.
func Redis() IRedis {
	if localRedis == nil {
		panic("implement not found for interface IRedis, forgot register?")
	}
	return localRedis
}
