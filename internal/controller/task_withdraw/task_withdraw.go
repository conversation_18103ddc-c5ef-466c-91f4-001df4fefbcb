package task_withdraw

import (
	"context"

	taskv1 "task-withdraw/api"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"google.golang.org/grpc"
)

type Controller struct {
	taskv1.UnimplementedTaskServiceServer
}

func Register(s *grpc.Server) {
	taskv1.RegisterTaskServiceServer(s, &Controller{})
}

func (*Controller) ListWithdrawals(ctx context.Context, req *taskv1.ListWithdrawalsRequest) (res *taskv1.ApiResponse, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) UpdateWithdrawalStatus(ctx context.Context, req *taskv1.UpdateWithdrawalStatusRequest) (res *taskv1.ApiResponse, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) ListMerchantWithdraws(ctx context.Context, req *taskv1.ListMerchantWithdrawsRequest) (res *taskv1.ApiResponse, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) UpdateMerchantWithdraw(ctx context.Context, req *taskv1.UpdateMerchantWithdrawRequest) (res *taskv1.ApiResponse, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) ListMerchantSettlements(ctx context.Context, req *taskv1.ListMerchantSettlementsRequest) (res *taskv1.ApiResponse, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) UpdateMerchantSettlement(ctx context.Context, req *taskv1.UpdateMerchantSettlementRequest) (res *taskv1.ApiResponse, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}
