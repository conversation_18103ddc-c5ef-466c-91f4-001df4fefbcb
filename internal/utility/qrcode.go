package utility

import (
	"github.com/skip2/go-qrcode"
)

// GenerateQRCode generates a QR code image for the given content string.
// It returns the QR code image data as PNG bytes and an error if generation fails.
// Default QR code size is 256x256 pixels with Medium recovery level.
func GenerateQRCode(content string) ([]byte, error) {
	// Generate QR code with default settings (256x256, Medium recovery level)
	pngBytes, err := qrcode.Encode(content, qrcode.Medium, 256)
	if err != nil {
		// Consider logging the error here if a logging mechanism is available
		return nil, err // Return nil bytes and the error
	}
	return pngBytes, nil
}
