package tron

import (
	"context"
	"crypto/tls"
	"fmt"
	"strconv" // Added for Itoa
	"strings"
	"time"

	"github.com/fbsobreira/gotron-sdk/pkg/client" // For GrpcClient type
	"github.com/gogf/gf/v2/frame/g"               // For config access
	"github.com/shopspring/decimal"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"

	tronwallet "task-withdraw/internal/utility/crypto/tron/tron-wallet" // Corrected import path and aliased
	// "encoding/json" // No longer directly used here
	// "io/ioutil"     // No longer directly used here
	// "net/http"      // No longer directly used here
)

// auth implements grpc.PerRPCCredentials for API key authentication
type auth struct {
	token string
}

func (a *auth) GetRequestMetadata(ctx context.Context, uri ...string) (map[string]string, error) {
	return map[string]string{
		"x-token": a.token,
	}, nil
}

func (a *auth) RequireTransportSecurity() bool {
	return true // Changed to true for TLS connections
}

// EnergyRentalOrderResponse represents the response from the energy rental API
type EnergyRentalOrderResponse struct {
	OrderId      string `json:"orderId"`
	EnergyAmount int    `json:"energyAmount"`
}

// RentEnergy rents energy for a TRON address
func RentEnergy(ctx context.Context, apiKey, apiSecret, apiBaseUrl, address string, amount int, duration string, trc20MaxEnergyFee decimal.Decimal) (*EnergyRentalOrderResponse, error) {

	//下单前获取预估的总价
	totalPrice, err := GetItrxEnergyPrice(ctx, apiKey, apiBaseUrl, amount, duration)
	if err != nil {
		return nil, fmt.Errorf("Failed to get energy price: %w", err)
	}
	if totalPrice.GreaterThan(trc20MaxEnergyFee) {
		return nil, fmt.Errorf("Energy price exceeds maximum allowed fee: %s > %s", totalPrice.String(), trc20MaxEnergyFee.String())
	}

	g.Log().Infof(ctx, "[LOG_DEBUG RentEnergy] Calling RentItrxEnergy with address: %s, amount: %d, duration: %s, apiBaseUrl: %s", address, amount, duration, apiBaseUrl)
	itrxResp, err := RentItrxEnergy(ctx, apiKey, apiSecret, apiBaseUrl, address, amount, duration)
	if itrxResp != nil {
		g.Log().Infof(ctx, "[LOG_DEBUG RentEnergy] Received from RentItrxEnergy: itrxResp.Serial: %s, itrxResp.Amount (int64): %d", itrxResp.Serial, itrxResp.Amount)
	}
	if err != nil {
		return nil, fmt.Errorf("RentEnergy via Itrx failed: %w", err)
	}

	if itrxResp == nil {
		return nil, fmt.Errorf("RentEnergy via Itrx returned nil response")
	}

	// Adapt ItrxOrderResponse to EnergyRentalOrderResponse
	return &EnergyRentalOrderResponse{
		OrderId:      itrxResp.Serial,
		EnergyAmount: int(itrxResp.Amount), // Assuming ItrxOrderResponse.Amount is compatible with int
	}, nil
}

// GetEnergyRentalStatus gets the status of an energy rental order
func GetEnergyRentalStatus(ctx context.Context, apiKey, apiSecret, apiBaseUrl, orderID string) (string, error) {
	itrxOrderStatusResp, err := QueryItrxOrderStatus(ctx, apiKey, apiBaseUrl, orderID)
	if err != nil {
		return "", fmt.Errorf("GetEnergyRentalStatus via Itrx failed: %w", err)
	}

	if itrxOrderStatusResp == nil {
		return "", fmt.Errorf("GetEnergyRentalStatus via Itrx returned nil response for orderID %s", orderID)
	}

	// Convert status int to string.
	// The specific mapping of int status to string status might need to be defined
	// based on iTRX API documentation or existing logic.
	// For now, just converting the int to string.
	// Example: 30 for success, 20 for in progress.
	// Caller of this utility function might need to interpret this int status.
	// Or this function could return the int status directly if that's more useful.
	// For now, returning the int status as a string.
	return strconv.Itoa(itrxOrderStatusResp.Status), nil
}

// GetAccountEnergy gets the energy of a TRON address
func GetAccountEnergy(ctx context.Context, address string, grpcNodeURL string, grpcApiKey string) (int64, error) {
	// Validate TRON address format first (basic check)
	if !strings.HasPrefix(address, "T") || len(address) != 34 {
		return 0, fmt.Errorf("invalid TRON address format for GetAccountEnergy: %s", address)
	}
	// A more robust validation might call tron.ValidateAddress if it's made available
	// or if this function is moved to a package with access to it.

	conn := tronwallet.NewGrpcClient(grpcNodeURL, 10*time.Second, grpcApiKey)
	if conn == nil {
		return 0, fmt.Errorf("failed to create TRON gRPC client for GetAccountEnergy")
	}

	// Start the connection with proper credentials
	// Note: The original NewGrpcClient in tronwallet doesn't Start the connection.
	// The caller of NewGrpcClient is expected to Start it.
	// This utility function will handle the Start and Stop.
	
	// Use TLS for QuikNode and other production endpoints
	var dialOpts []grpc.DialOption
	if strings.Contains(grpcNodeURL, "quiknode") || strings.Contains(grpcNodeURL, "trongrid") {
		// Use TLS for production endpoints
		dialOpts = []grpc.DialOption{
			grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{})),
		}
	} else {
		// Use insecure for local or test endpoints
		dialOpts = []grpc.DialOption{
			grpc.WithTransportCredentials(insecure.NewCredentials()),
		}
	}
	
	// Add API key if provided
	if grpcApiKey != "" {
		dialOpts = append(dialOpts, grpc.WithPerRPCCredentials(&auth{token: grpcApiKey}))
	}
	
	err := conn.Start(dialOpts...)
	if err != nil {
		return 0, fmt.Errorf("failed to start TRON gRPC client for GetAccountEnergy: %w", err)
	}
	defer conn.Stop()

	accRes, err := conn.GetAccountResource(address)
	if err != nil {
		if strings.Contains(err.Error(), "account not found") {
			// Consider account not found as 0 energy, not an error for this specific function's purpose.
			return 0, nil
		}
		return 0, fmt.Errorf("failed to get account resource for GetAccountEnergy: %w", err)
	}

	// Energy is EnergyLimit - EnergyUsed
	// Note: The original placeholder returned EnergyLimit.
	// The actual available energy for transactions is typically EnergyLimit - EnergyUsed.
	// If the intent was total capacity, then GetEnergyLimit() is correct.
	// If it's usable energy, then GetEnergyLimit() - GetEnergyUsed() is more appropriate.
	// The original placeholder parsed "EnergyLimit" from a mock JSON.
	// The gRPC response provides GetEnergyLimit() and GetEnergyUsed().
	// Let's return available energy.
	availableEnergy := accRes.GetEnergyLimit() - accRes.GetEnergyUsed()
	return availableEnergy, nil
}

// GetAccountBandwidth gets the bandwidth of a TRON address
func GetAccountBandwidth(ctx context.Context, address string, grpcNodeURL string, grpcApiKey string) (int64, error) {
	// Validate TRON address format first (basic check)
	if !strings.HasPrefix(address, "T") || len(address) != 34 {
		return 0, fmt.Errorf("invalid TRON address format for GetAccountBandwidth: %s", address)
	}

	conn := tronwallet.NewGrpcClient(grpcNodeURL, 10*time.Second, grpcApiKey)
	if conn == nil {
		return 0, fmt.Errorf("failed to create TRON gRPC client for GetAccountBandwidth")
	}

	// Start the connection with proper credentials
	// Use TLS for QuikNode and other production endpoints
	var dialOpts []grpc.DialOption
	if strings.Contains(grpcNodeURL, "quiknode") || strings.Contains(grpcNodeURL, "trongrid") {
		// Use TLS for production endpoints
		dialOpts = []grpc.DialOption{
			grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{})),
		}
	} else {
		// Use insecure for local or test endpoints
		dialOpts = []grpc.DialOption{
			grpc.WithTransportCredentials(insecure.NewCredentials()),
		}
	}
	
	// Add API key if provided
	if grpcApiKey != "" {
		dialOpts = append(dialOpts, grpc.WithPerRPCCredentials(&auth{token: grpcApiKey}))
	}
	
	err := conn.Start(dialOpts...)
	if err != nil {
		return 0, fmt.Errorf("failed to start TRON gRPC client for GetAccountBandwidth: %w", err)
	}
	defer conn.Stop()

	accRes, err := conn.GetAccountResource(address)
	if err != nil {
		if strings.Contains(err.Error(), "account not found") {
			// Consider account not found as 0 bandwidth, not an error for this specific function's purpose.
			return 0, nil
		}
		return 0, fmt.Errorf("failed to get account resource for GetAccountBandwidth: %w", err)
	}

	// Calculate available bandwidth (NetLimit - NetUsed + FreeNetLimit - FreeNetUsed)
	availableBandwidth := accRes.GetNetLimit() - accRes.GetNetUsed() + accRes.GetFreeNetLimit() - accRes.GetFreeNetUsed()
	return availableBandwidth, nil
}

// GetTronGrpcClientFromConfig creates and starts a TRON gRPC client using settings from the specified configuration prefix.
// The caller is responsible for calling conn.Stop() on the returned client when done.
func GetTronGrpcClientFromConfig(ctx context.Context, configPrefix string) (*client.GrpcClient, error) {
	grpcNodeURL := g.Cfg().MustGet(ctx, configPrefix+".grpcNodeURL", "").String()
	if grpcNodeURL == "" {
		// Fallback to a more general node URL if specific one isn't found, e.g. from deposit check config
		// This fallback might need adjustment based on actual config structure
		grpcNodeURL = g.Cfg().MustGet(ctx, "depositCheck.chains.TRON.rpc", "").String()
		if grpcNodeURL == "" {
			return nil, fmt.Errorf("TRON gRPC node URL not configured under %s.grpcNodeURL or depositCheck.chains.TRON.rpc", configPrefix)
		}
	}

	grpcApiKey := g.Cfg().MustGet(ctx, configPrefix+".grpcApiKey", "").String() // API key might be optional for some nodes

	// Default timeout, can also be made configurable if needed
	timeout := 10 * time.Second

	conn := tronwallet.NewGrpcClient(grpcNodeURL, timeout, grpcApiKey)
	if conn == nil {
		return nil, fmt.Errorf("failed to create TRON gRPC client using config prefix %s (nodeURL: %s)", configPrefix, grpcNodeURL)
	}

	// Start the connection
	// Note: The NewGrpcClient from tronwallet package does not start the connection.
	// Use TLS for QuikNode and other production endpoints
	var dialOpts []grpc.DialOption
	if strings.Contains(grpcNodeURL, "quiknode") || strings.Contains(grpcNodeURL, "trongrid") {
		// Use TLS for production endpoints
		dialOpts = []grpc.DialOption{
			grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{})),
		}
	} else {
		// Use insecure for local or test endpoints
		dialOpts = []grpc.DialOption{
			grpc.WithTransportCredentials(insecure.NewCredentials()),
		}
	}
	
	// Add API key if provided
	if grpcApiKey != "" {
		dialOpts = append(dialOpts, grpc.WithPerRPCCredentials(&auth{token: grpcApiKey}))
	}
	
	err := conn.Start(dialOpts...)
	if err != nil {
		// It's good practice to try and stop the connection if starting failed, though Stop might also fail or be no-op
		// conn.Stop() // This might not be necessary or could panic if conn is not in a stoppable state.
		return nil, fmt.Errorf("failed to start TRON gRPC client (nodeURL: %s): %w", grpcNodeURL, err)
	}

	return conn, nil
}

// IsAddressActive checks if a TRON address is active on the network.
// An address is considered active if GetAccount API call is successful and returns account information.
func IsAddressActive(ctx context.Context, address string, grpcNodeURL string, grpcApiKey string) (bool, error) {
	// Validate TRON address format first (basic check)
	if !strings.HasPrefix(address, "T") || len(address) != 34 {
		return false, fmt.Errorf("invalid TRON address format for IsAddressActive: %s", address)
	}

	conn := tronwallet.NewGrpcClient(grpcNodeURL, 10*time.Second, grpcApiKey)
	if conn == nil {
		return false, fmt.Errorf("failed to create TRON gRPC client for IsAddressActive")
	}

	// Use TLS for QuikNode and other production endpoints
	var dialOpts []grpc.DialOption
	if strings.Contains(grpcNodeURL, "quiknode") || strings.Contains(grpcNodeURL, "trongrid") {
		// Use TLS for production endpoints
		dialOpts = []grpc.DialOption{
			grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{})),
		}
	} else {
		// Use insecure for local or test endpoints
		dialOpts = []grpc.DialOption{
			grpc.WithTransportCredentials(insecure.NewCredentials()),
		}
	}
	
	// Add API key if provided
	if grpcApiKey != "" {
		dialOpts = append(dialOpts, grpc.WithPerRPCCredentials(&auth{token: grpcApiKey}))
	}
	
	err := conn.Start(dialOpts...)
	if err != nil {
		return false, fmt.Errorf("failed to start TRON gRPC client for IsAddressActive: %w", err)
	}
	defer conn.Stop()

	// Attempt to get account information.
	// If the account exists on the blockchain, this call should succeed.
	// For an address to be "active" in a way that it can receive energy or participate in transactions,
	// it generally needs to exist on-chain. `GetAccount` is a good way to check this.
	accInfo, err := conn.GetAccount(address)
	if err != nil {
		// Check for specific error messages indicating the account does not exist.
		// These messages can vary slightly depending on the node implementation or gRPC library version.
		errStr := strings.ToLower(err.Error())
		if strings.Contains(errStr, "account not found") || strings.Contains(errStr, "account does not exist") || strings.Contains(errStr, "empty address") {
			// This indicates the address is not known to the network, hence not active.
			return false, nil
		}
		// For other errors, propagate them.
		return false, fmt.Errorf("failed to get account info for IsAddressActive: %w", err)
	}

	// If GetAccount succeeds and returns non-nil account info, the address is considered active.
	// An account might exist but have zero balance, zero energy, etc.
	// For the purpose of "activation" in the context of being able to receive energy or interact,
	// its existence on the chain is the primary factor.
	if accInfo == nil {
		// This case should ideally not happen if err is nil, but as a safeguard:
		return false, fmt.Errorf("GetAccount returned nil info without error for address %s", address)
	}

	// Address exists on the blockchain.
	return true, nil
}
