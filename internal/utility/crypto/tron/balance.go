package tron

import (
	"context"
	"strings"
	util "task-withdraw/internal/utility/utils"
	"time"

	"github.com/fbsobreira/gotron-sdk/pkg/client"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// createGrpcClient creates a new gRPC client with proper connection handling
func createGrpcClient(ctx context.Context) (*client.GrpcClient, error) {
	grpcUrl, err := g.Cfg().Get(ctx, "tron_grpc_rpc_url")
	if err != nil {
		return nil, gerror.Wrap(err, "failed to get tron_grpc_rpc_url config")
	}

	// Create a new gRPC client with timeout
	conn := client.NewGrpcClientWithTimeout(grpcUrl.String(), 10*time.Second)

	// Start the connection with proper credentials
	err = conn.Start(grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, gerror.Wrap(err, "failed to start grpc client")
	}

	return conn, nil
}

// GetTRXBalance returns the TRX balance for a given address as a decimal.Decimal
// This function requires proper configuration to create a TronSender instance
func GetTRXBalance(ctx context.Context, address string) (decimal.Decimal, error) {
	// Validate TRON address format first
	if !ValidateAddress(address) {
		return decimal.Zero, gerror.New("failed to get TRX balance: b58 check error")
	}

	// Create a new gRPC client
	conn, err := createGrpcClient(ctx)
	if err != nil {
		return decimal.Zero, err
	}
	// Ensure connection is closed when function returns
	defer conn.Conn.Close()

	// Get account information
	account, err := conn.GetAccount(address)
	if err != nil {
		if strings.Contains(err.Error(), "account not found") {
			glog.Errorf(ctx, "Account %s not found on chain, returning zero balance.", address)
			return decimal.Zero, nil
		}
		return decimal.Zero, gerror.Wrapf(err, "failed to get TRX account info for %s", address)
	}

	balanceSun := account.GetBalance()

	decimals, err := util.GetTokenDecimals(ctx, "TRC20USDT")
	if err != nil {
		return decimal.Zero, err
	}

	// TRX always has 6 decimals (Sun)
	balanceDecimal, err := util.FormatTokenValueToDecimal(decimal.NewFromInt(balanceSun), decimals)

	if err != nil {
		return decimal.Zero, err
	}

	glog.Debugf(ctx, "TRX Balance: %s TRX (Sun: %d)", balanceDecimal.String(), balanceSun)
	return balanceDecimal, nil
}

// GetTRC20UsdtTokenBalance gets the TRC20 USDT token balance for a given address
func GetTRC20UsdtTokenBalance(ctx context.Context, address string) (decimal.Decimal, error) {
	// Validate TRON address format first
	if !ValidateAddress(address) {
		return decimal.Zero, gerror.New("failed to get TRC20 balance: b58 check error")
	}

	// Create a new gRPC client
	conn, err := createGrpcClient(ctx)
	if err != nil {
		return decimal.Zero, err
	}
	// Ensure connection is closed when function returns
	defer conn.Conn.Close()

	// Get TRC20 contract address from config
	trc20Contract, err := g.Cfg().Get(ctx, "usdt_trc20_contract")
	if err != nil {
		return decimal.Zero, gerror.Wrap(err, "failed to get usdt_trc20_contract config")
	}

	// Log the address and contract for debugging
	glog.Debugf(ctx, "Querying TRC20 balance for address: %s, contract: %s", address, trc20Contract.String())

	// Get TRC20 token balance
	balance, err := conn.TRC20ContractBalance(address, trc20Contract.String())
	if err != nil {
		if strings.Contains(err.Error(), "account not found") {
			glog.Warningf(ctx, "Account %s not found on chain for TRC20 balance check, returning zero balance.", address)
			return decimal.Zero, nil
		}
		return decimal.Zero, gerror.Wrap(err, "failed to get TRC20 balance")
	}

	// Get token decimals
	decimals, err := conn.TRC20GetDecimals(trc20Contract.String())
	if err != nil {
		return decimal.Zero, gerror.Wrap(err, "failed to get TRC20 decimals")
	}

	balanceDecimal := decimal.NewFromInt(balance.Int64()).Div(decimal.New(1, 0))
	// 除以 10 的 decimals 次方
	balanceDecimal = balanceDecimal.Div(decimal.New(1, int32(decimals.Int64())))
	glog.Debugf(ctx, "TRC20 USDT Balance for %s: %s (raw: %d)", address, balanceDecimal.String(), balance.Int64())
	return balanceDecimal, nil
}

// GetTRC20UsdtBalance gets the TRC20 USDT token balance for a given address with a specific contract
func GetTRC20UsdtBalance(address string, contractAddress string) (string, error) {
	ctx := context.Background()

	// Validate TRON address format first
	if !ValidateAddress(address) {
		return "0", gerror.New("failed to get TRC20 balance: b58 check error")
	}

	// Validate contract address format (basic check)
	if !strings.HasPrefix(contractAddress, "T") || len(contractAddress) != 34 {
		return "0", gerror.New("invalid TRC20 contract address format")
	}

	// Create a new gRPC client
	conn, err := createGrpcClient(ctx)
	if err != nil {
		return "0", err
	}
	// Ensure connection is closed when function returns
	defer conn.Conn.Close()

	// Log the address and contract for debugging
	glog.Debugf(ctx, "Querying TRC20 balance for address: %s, contract: %s", address, contractAddress)

	// Get TRC20 token balance
	balance, err := conn.TRC20ContractBalance(address, contractAddress)
	if err != nil {
		if strings.Contains(err.Error(), "account not found") {
			glog.Warningf(ctx, "Account %s not found on chain for TRC20 balance check, returning zero balance.", address)
			return "0", nil
		}
		return "0", gerror.Wrap(err, "failed to get TRC20 balance")
	}

	// Get token decimals
	decimals, err := conn.TRC20GetDecimals(contractAddress)
	if err != nil {
		return "0", gerror.Wrap(err, "failed to get TRC20 decimals")
	}

	balanceDecimal := decimal.NewFromInt(balance.Int64()).Div(decimal.New(1, 0))
	// 除以 10 的 decimals 次方
	balanceDecimal = balanceDecimal.Div(decimal.New(1, int32(decimals.Int64())))
	glog.Debugf(ctx, "TRC20 USDT Balance for %s: %s (raw: %d)", address, balanceDecimal.String(), balance.Int64())

	return balanceDecimal.String(), nil
}
