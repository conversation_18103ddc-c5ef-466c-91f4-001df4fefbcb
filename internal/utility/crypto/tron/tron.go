package tron

import (
	"bytes"
	"crypto/ecdsa"
	"crypto/sha256"
	"encoding/hex"

	// "errors" // Removed unused import
	"fmt"
	"math/big"
	"strings"

	// "task-withdraw/internal/codes" // Import codes package
	// New import for TRON derivation
	// New import for TRON derivation

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/gogf/gf/v2/errors/gerror"
)

// Base58解码实现
var b58Alphabet = []byte("**********************************************************")

// DecodeBase58 将Base58字符串解码为字节数组
func DecodeBase58(input string) ([]byte, error) {
	result := big.NewInt(0)
	zeroBytes := 0

	for _, r := range input {
		if r == '1' {
			zeroBytes++
		} else {
			break
		}
	}

	payload := input[zeroBytes:]
	for _, r := range payload {
		charIndex := strings.IndexByte(string(b58Alphabet), byte(r))
		if charIndex < 0 {
			return nil, gerror.Newf("Invalid character in Base58 string: %c", r)
		}
		result.Mul(result, big.NewInt(58))
		result.Add(result, big.NewInt(int64(charIndex)))
	}

	decoded := result.Bytes()
	combined := make([]byte, zeroBytes+len(decoded))
	copy(combined[zeroBytes:], decoded)

	return combined, nil
}

func ValidatePrivateKey(privateKey string) bool {
	// 验证私钥是否有效

	// 移除可能的0x前缀
	cleanPrivateKey := strings.TrimPrefix(privateKey, "0x")

	// 私钥必须是64个十六进制字符（32字节）
	if len(cleanPrivateKey) != 64 {
		return false
	}

	// 检查是否是有效的十六进制字符串
	_, err := hex.DecodeString(cleanPrivateKey)
	if err != nil {
		return false
	}

	// 检查私钥是否在有效范围内（小于secp256k1曲线的阶）
	n := new(big.Int)
	n, ok := n.SetString(cleanPrivateKey, 16)
	if !ok {
		return false
	}

	// secp256k1曲线的阶
	// N = FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
	curveOrder, _ := new(big.Int).SetString("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141", 16)

	// 私钥必须大于0且小于曲线的阶
	if n.Cmp(big.NewInt(0)) <= 0 || n.Cmp(curveOrder) >= 0 {
		return false
	}

	// 尝试从私钥导出公钥，如果成功则私钥有效
	_, err = crypto.HexToECDSA(cleanPrivateKey)
	return err == nil
}

func ValidateAddress(address string) bool {
	// TRON地址验证

	// 检查地址是否以'T'开头
	if len(address) == 0 || address[0] != 'T' {
		return false
	}

	// TRON地址应该是34个字符
	if len(address) != 34 {
		return false
	}

	// 尝试对Base58地址进行解码
	decoded, err := DecodeBase58(address)
	if err != nil {
		return false
	}

	// 解码后应该是25个字节
	// 格式: [前缀(1字节)][地址(20字节)][校验和(4字节)]
	if len(decoded) != 25 {
		return false
	}

	// 检查前缀，TRON使用0x41(65)作为地址前缀
	if decoded[0] != 0x41 {
		return false
	}

	// 验证校验和
	// 取前21个字节进行两次SHA256哈希，结果的前4个字节应该与最后4个字节相等
	addressBytes := decoded[:21]
	checksum := decoded[21:]

	h := sha256.Sum256(addressBytes)
	h2 := sha256.Sum256(h[:])
	calculatedChecksum := h2[:4]

	return bytes.Equal(checksum, calculatedChecksum)
}

// GenerateAddress 生成TRON地址
func GenerateAddress(privateKey *ecdsa.PrivateKey) map[string]string {
	// 生成随机的私钥
	// privateKey, _ := crypto.GenerateKey()

	// 将私钥转换为十六进制字符串
	privateKeyBytes := crypto.FromECDSA(privateKey)
	privateKeyHex := hex.EncodeToString(privateKeyBytes)

	// 从私钥获取公钥并直接获取地址
	ethAddress := crypto.PubkeyToAddress(privateKey.PublicKey)
	ethAddressBytes := ethAddress.Bytes()

	// 转换为TRON格式地址
	// TRON地址前缀为0x41
	tronAddressBytes := append([]byte{0x41}, ethAddressBytes...)

	// 计算校验和
	h := sha256.Sum256(tronAddressBytes)
	h2 := sha256.Sum256(h[:])
	checksum := h2[:4]

	// 合并地址和校验和
	fullAddressBytes := append(tronAddressBytes, checksum...)

	// Base58编码
	address := encodeBase58(fullAddressBytes)

	return map[string]string{
		"address":     address,
		"private_key": privateKeyHex,
	}
}

// encodeBase58 将字节数组编码为Base58字符串
func encodeBase58(input []byte) string {
	// Base58编码实现
	bigInt := new(big.Int).SetBytes(input)
	var result []byte

	base := big.NewInt(58)
	zero := big.NewInt(0)

	for bigInt.Cmp(zero) > 0 {
		mod := new(big.Int)
		bigInt.DivMod(bigInt, base, mod)
		result = append(result, b58Alphabet[mod.Int64()])
	}

	// 反转结果
	for i, j := 0, len(result)-1; i < j; i, j = i+1, j-1 {
		result[i], result[j] = result[j], result[i]
	}

	return string(result)
}

// PublicKeyToTronAddress converts an ECDSA public key to a TRON Base58 address string.
func PublicKeyToTronAddress(publicKey *ecdsa.PublicKey) string {
	ethAddress := crypto.PubkeyToAddress(*publicKey)
	ethAddressBytes := ethAddress.Bytes()

	// TRON address prefix is 0x41
	tronAddressBytes := append([]byte{0x41}, ethAddressBytes...)

	// Calculate checksum
	h := sha256.Sum256(tronAddressBytes)
	h2 := sha256.Sum256(h[:])
	checksum := h2[:4]

	// Append checksum
	fullAddressBytes := append(tronAddressBytes, checksum...)

	// Base58 encode
	return encodeBase58(fullAddressBytes)
}

// GetTronDerivedPath returns the derivation path for TRON.
func GetTronDerivedPath(index int) string {
	return fmt.Sprintf("m/44'/195'/0'/0/%d", index) // TRON's coin_type is 195
}

func GetTronFeeAddress(privateKey string) (string, error) {
	// 获取TRON矿工费地址

	// 移除可能的0x前缀
	cleanPrivateKey := strings.TrimPrefix(privateKey, "0x")

	// 将私钥转换为ECDSA私钥对象
	privateKeyECDSA, err := crypto.HexToECDSA(cleanPrivateKey)
	if err != nil {
		return "", err
	}

	// 从私钥获取公钥并生成以太坊格式地址
	ethAddress := crypto.PubkeyToAddress(privateKeyECDSA.PublicKey)
	ethAddressBytes := ethAddress.Bytes()

	// 转换为TRON格式地址
	// TRON地址前缀为0x41
	tronAddressBytes := append([]byte{0x41}, ethAddressBytes...)

	// 计算校验和
	h := sha256.Sum256(tronAddressBytes)
	h2 := sha256.Sum256(h[:])
	checksum := h2[:4]

	// 合并地址和校验和
	fullAddressBytes := append(tronAddressBytes, checksum...)

	// Base58编码
	address := encodeBase58(fullAddressBytes)

	return address, nil
}

func GetAddressFromPrivateKey(privateKeyStr string) (string, error) {
	// 移除可能的0x前缀
	cleanPrivateKey := strings.TrimPrefix(privateKeyStr, "0x")

	// 将私钥转换为ECDSA私钥对象
	privateKeyECDSA, err := crypto.HexToECDSA(cleanPrivateKey)
	if err != nil {
		return "", fmt.Errorf("无效的私钥: %v", err)
	}

	// 从私钥获取公钥并生成以太坊格式地址
	ethAddress := crypto.PubkeyToAddress(privateKeyECDSA.PublicKey)
	ethAddressBytes := ethAddress.Bytes()

	// 转换为TRON格式地址
	// TRON地址前缀为0x41
	tronAddressBytes := append([]byte{0x41}, ethAddressBytes...)

	// 计算校验和
	h := sha256.Sum256(tronAddressBytes)
	h2 := sha256.Sum256(h[:])
	checksum := h2[:4]

	// 合并地址和校验和
	fullAddressBytes := append(tronAddressBytes, checksum...)

	// Base58编码
	address := encodeBase58(fullAddressBytes)

	return address, nil
}
