package tron

import (
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
)

// GetTronHttpRpcUrl returns the HTTP RPC URL for TRON from config
func GetTronHttpRpcUrl(ctx context.Context) (string, error) {
	rpcUrl, err := g.Cfg().Get(ctx, "depositCheck.chains.TRON.rpc")
	if err != nil {
		return "", fmt.Errorf("failed to get TRON HTTP RPC URL from config: %w", err)
	}

	if rpcUrl.IsEmpty() {
		return "", fmt.Errorf("TRON HTTP RPC URL is empty in config")
	}

	return rpcUrl.String(), nil
}

// GetTronGrpcRpcUrl returns the gRPC URL for TRON from config
func GetTronGrpcRpcUrl(ctx context.Context) (string, error) {
	rpcUrl, err := g.Cfg().Get(ctx, "tron_grpc_rpc_url")
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("failed to get TRON gRPC URL from config: %w", err)
	}

	if rpcUrl.IsEmpty() {
		return "", fmt.Errorf("TRON gRPC URL is empty in config")
	}

	return rpcUrl.String(), nil
}

// GetTronApiKey returns the API key for TRON from config
func GetTronApiKey(ctx context.Context) (string, error) {
	apiKey, err := g.Cfg().Get(ctx, "depositCheck.chains.TRON.apiKey")
	if err != nil {
		return "", fmt.Errorf("failed to get TRON API key from config: %w", err)
	}

	// API key can be empty, so we don't check for that
	return apiKey.String(), nil
}

// GetContractAddress returns the contract address for a given token on TRON
func GetContractAddress(ctx context.Context, chain string, token string) (string, error) {
	// Normalize chain and token names to uppercase for consistency
	chainUpper := strings.ToUpper(chain)
	tokenUpper := strings.ToUpper(token)

	// Ensure we're using TRON as the chain name (not TRX)
	if chainUpper == "TRX" {
		chainUpper = "TRON"
	}

	configPath := fmt.Sprintf("depositCheck.chains.%s.tokens.%s.contractAddress", chainUpper, strings.ToLower(tokenUpper))
	contractAddr, err := g.Cfg().Get(ctx, configPath)
	if err != nil {
		return "", fmt.Errorf("failed to get %s contract address for %s from config: %w", tokenUpper, chainUpper, err)
	}

	if contractAddr.IsEmpty() {
		return "", fmt.Errorf("%s contract address for %s is empty in config", tokenUpper, chainUpper)
	}

	return contractAddr.String(), nil
}

// GetTokenDecimals returns the number of decimals for a given token on TRON
func GetTokenDecimals(ctx context.Context, chain string, token string) (int, error) {
	// Normalize chain and token names to uppercase for consistency
	chainUpper := strings.ToUpper(chain)
	tokenUpper := strings.ToUpper(token)

	// Ensure we're using TRON as the chain name (not TRX)
	if chainUpper == "TRX" {
		chainUpper = "TRON"
	}

	var configPath string
	if tokenUpper == "TRX" {
		// For native TRX, use the tron_decimals config
		configPath = "tron_decimals"
	} else {
		// For tokens like USDT, use the token-specific config
		configPath = fmt.Sprintf("depositCheck.chains.%s.tokens.%s.decimals", chainUpper, strings.ToLower(tokenUpper))
	}

	decimals, err := g.Cfg().Get(ctx, configPath)
	if err != nil {
		return 0, fmt.Errorf("failed to get %s decimals for %s from config: %w", tokenUpper, chainUpper, err)
	}

	if decimals.IsEmpty() {
		// Default to 6 decimals if not specified
		return 6, nil
	}

	return decimals.Int(), nil
}

// GetConfirmations returns the number of confirmations required for a given token on TRON
func GetConfirmations(ctx context.Context, chain string, token string) (int, error) {
	// Normalize chain and token names to uppercase for consistency
	chainUpper := strings.ToUpper(chain)
	tokenUpper := strings.ToUpper(token)

	// Ensure we're using TRON as the chain name (not TRX)
	if chainUpper == "TRX" {
		chainUpper = "TRON"
	}

	var configPath string
	if tokenUpper == "TRX" {
		// For native TRX, use the chain-level confirmations
		configPath = fmt.Sprintf("depositCheck.chains.%s.confirmations", chainUpper)
	} else {
		// For tokens like USDT, use the token-specific confirmations if available
		configPath = fmt.Sprintf("depositCheck.chains.%s.tokens.%s.confirmations", chainUpper, strings.ToLower(tokenUpper))

		// Check if token-specific confirmations exist
		confirmations, err := g.Cfg().Get(ctx, configPath)
		if err != nil || confirmations.IsEmpty() {
			// Fall back to chain-level confirmations
			configPath = fmt.Sprintf("depositCheck.chains.%s.confirmations", chainUpper)
		}
	}

	confirmations, err := g.Cfg().Get(ctx, configPath)
	if err != nil {
		return 0, fmt.Errorf("failed to get confirmations for %s on %s from config: %w", tokenUpper, chainUpper, err)
	}

	if confirmations.IsEmpty() {
		// Default to 19 confirmations if not specified (common for TRON)
		return 19, nil
	}

	return confirmations.Int(), nil
}
