package eth

// import (
// 	"context"
// 	"crypto/ecdsa"
// 	"fmt"
// 	"task-withdraw/internal/dao"
// 	"task-withdraw/internal/model/entity"

// 	"github.com/btcsuite/btcd/btcutil/hdkeychain"
// 	"github.com/btcsuite/btcd/chaincfg"
// 	"github.com/ethereum/go-ethereum/accounts"
// 	"github.com/ethereum/go-ethereum/common/hexutil"
// 	"github.com/ethereum/go-ethereum/crypto"
// 	"github.com/gogf/gf/v2/errors/gerror"
// 	"github.com/gogf/gf/v2/frame/g"
// 	"github.com/gogf/gf/v2/os/glog"
// "task-withdraw/internal/utility/utils/bip39" // Added for NewSeed

// )

// // GetDerivedPrivateKey derives a private key from the wallet mnemonic and path
// func GetDerivedPrivateKey(ctx context.Context, addressPath int) (string, string, error) {
// 	// Get wallet mnemonic from config
// 	mnemonic, err := getWalletMnemonic(ctx)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to get wallet mnemonic")
// 	}

// 	// Validate mnemonic
// 	if !bip39.IsMnemonicValid(mnemonic) {
// 		return "", "", gerror.New("invalid mnemonic")
// 	}

// 	// Generate seed from mnemonic
// 	seed := bip39.NewSeed(mnemonic, "")

// 	// Create master key from seed
// 	masterKey, err := hdkeychain.NewMaster(seed, &chaincfg.MainNetParams)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to create master key from seed")
// 	}

// 	// Derive child key using path
// 	// For Ethereum, the path is m/44'/60'/0'/0/i where i is the account index
// 	// Derive purpose
// 	purpose, err := masterKey.Derive(hdkeychain.HardenedKeyStart + 44)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to derive purpose")
// 	}

// 	// Derive coin type (60' for Ethereum)
// 	coinType, err := purpose.Derive(hdkeychain.HardenedKeyStart + 60)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to derive coin type")
// 	}

// 	// Derive account (0')
// 	account, err := coinType.Derive(hdkeychain.HardenedKeyStart + 0)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to derive account")
// 	}

// 	// Derive change (0 for external)
// 	change, err := account.Derive(0)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to derive change")
// 	}

// 	// Derive address index
// 	addressKey, err := change.Derive(uint32(addressPath))
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to derive address index")
// 	}

// 	// Get private key
// 	privateKeyECDSA, err := addressKey.ECPrivKey()
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to get private key")
// 	}

// 	// Convert to Ethereum private key
// 	privateKey := privateKeyECDSA.ToECDSA()

// 	// Convert private key to string
// 	privateKeyBytes := crypto.FromECDSA(privateKey)
// 	privateKeyHex := hexutil.Encode(privateKeyBytes)[2:] // Remove 0x prefix

// 	// Get address from private key
// 	publicKey := privateKey.Public()
// 	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
// 	if !ok {
// 		return "", "", gerror.New("failed to cast public key to ECDSA")
// 	}
// 	address := crypto.PubkeyToAddress(*publicKeyECDSA).Hex()

// 	derivationPath := fmt.Sprintf("m/44'/60'/0'/0/%d", addressPath)
// 	glog.Infof(ctx, "Derived ETH address %s for path %s", address, derivationPath)

// 	return privateKeyHex, address, nil
// }

// // PrivateKeyFromString converts a private key string to an ECDSA private key
// func PrivateKeyFromString(privateKeyHex string) (*ecdsa.PrivateKey, error) {
// 	return crypto.HexToECDSA(privateKeyHex)
// }

// // getWalletMnemonic gets the wallet mnemonic from the database
// func getWalletMnemonic(ctx context.Context) (string, error) {
// 	// Get wallet ID from config
// 	walletID := g.Cfg().MustGet(ctx, "wallet.id", 1).Int()

// 	// Get wallet from database
// 	var wallet entity.Wallets
// 	err := dao.Wallets.Ctx(ctx).Where(dao.Wallets.Columns().Id, walletID).Scan(&wallet)
// 	if err != nil {
// 		return "", gerror.Wrapf(err, "failed to get wallet with ID %d", walletID)
// 	}

// 	// Get mnemonic
// 	mnemonic := wallet.Mnemonic
// 	if mnemonic == "" {
// 		return "", gerror.Newf("wallet with ID %d has empty mnemonic", walletID)
// 	}

// 	return mnemonic, nil
// }

// // GetAddressForPath gets the address for a given path
// func GetAddressForPath(ctx context.Context, path int) (string, error) {
// 	_, address, err := GetDerivedPrivateKey(ctx, path)
// 	if err != nil {
// 		return "", gerror.Wrapf(err, "failed to derive private key for path %d", path)
// 	}
// 	return address, nil
// }

// // GetPathForAddress gets the path for a given address
// func GetPathForAddress(ctx context.Context, address string) (int, error) {
// 	// Query the address table
// 	var addressEntity entity.Address
// 	err := dao.Address.Ctx(ctx).
// 		Where(dao.Address.Columns().Address, address).
// 		Where(dao.Address.Columns().Type, "ETH").
// 		Scan(&addressEntity)
// 	if err != nil {
// 		return 0, gerror.Wrapf(err, "failed to get address entity for address %s", address)
// 	}

// 	return addressEntity.Path, nil
// }

// // DeriveAddressFromPath derives an Ethereum address from a derivation path
// func DeriveAddressFromPath(mnemonic string, path accounts.DerivationPath) (string, error) {
// 	// Validate mnemonic
// 	if !bip39.IsMnemonicValid(mnemonic) {
// 		return "", gerror.New("invalid mnemonic")
// 	}

// 	// Generate seed from mnemonic
// 	seed := bip39.NewSeed(mnemonic, "")

// 	// Create master key from seed
// 	masterKey, err := hdkeychain.NewMaster(seed, &chaincfg.MainNetParams)
// 	if err != nil {
// 		return "", gerror.Wrapf(err, "failed to create master key from seed")
// 	}

// 	// Derive child keys using path
// 	key := masterKey
// 	for _, n := range path {
// 		key, err = key.Derive(n)
// 		if err != nil {
// 			return "", gerror.Wrapf(err, "failed to derive key for path component %d", n)
// 		}
// 	}

// 	// Get private key
// 	privateKeyECDSA, err := key.ECPrivKey()
// 	if err != nil {
// 		return "", gerror.Wrapf(err, "failed to get private key")
// 	}

// 	// Convert to Ethereum private key
// 	privateKey := privateKeyECDSA.ToECDSA()

// 	// Get address from private key
// 	publicKey := privateKey.Public()
// 	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
// 	if !ok {
// 		return "", gerror.New("failed to cast public key to ECDSA")
// 	}
// 	address := crypto.PubkeyToAddress(*publicKeyECDSA).Hex()

// 	return address, nil
// }
