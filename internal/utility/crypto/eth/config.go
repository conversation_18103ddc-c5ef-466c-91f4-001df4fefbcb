package eth

import (
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
)

// GetEthHttpRpcUrl returns the HTTP RPC URL for Ethereum from config
func GetEthHttpRpcUrl(ctx context.Context) (string, error) {
	rpcUrl, err := g.Cfg().Get(ctx, "depositCheck.chains.ETH.rpc")
	if err != nil {
		return "", fmt.Errorf("failed to get ETH HTTP RPC URL from config: %w", err)
	}

	if rpcUrl.IsEmpty() {
		return "", fmt.Errorf("ETH HTTP RPC URL is empty in config")
	}

	return rpcUrl.String(), nil
}

// GetEthApiKey returns the API key for Ethereum from config (if any)
func GetEthApiKey(ctx context.Context) (string, error) {
	apiKey, err := g.Cfg().Get(ctx, "depositCheck.chains.ETH.apiKey")
	if err != nil {
		return "", fmt.Errorf("failed to get ETH API key from config: %w", err)
	}

	// API key can be empty, so we don't check for that
	return apiKey.String(), nil
}

// GetContractAddress returns the contract address for a given token on Ethereum
func GetContractAddress(ctx context.Context, chain string, token string) (string, error) {
	// Normalize chain and token names to uppercase for consistency
	chainUpper := strings.ToUpper(chain)
	tokenUpper := strings.ToUpper(token)

	configPath := fmt.Sprintf("depositCheck.chains.%s.tokens.%s.contractAddress", chainUpper, strings.ToLower(tokenUpper))
	contractAddr, err := g.Cfg().Get(ctx, configPath)
	if err != nil {
		return "", fmt.Errorf("failed to get %s contract address for %s from config: %w", tokenUpper, chainUpper, err)
	}

	if contractAddr.IsEmpty() {
		return "", fmt.Errorf("%s contract address for %s is empty in config", tokenUpper, chainUpper)
	}

	return contractAddr.String(), nil
}

// GetTokenDecimals returns the number of decimals for a given token on Ethereum
func GetTokenDecimals(ctx context.Context, chain string, token string) (int, error) {
	// Normalize chain and token names to uppercase for consistency
	chainUpper := strings.ToUpper(chain)
	tokenUpper := strings.ToUpper(token)

	var configPath string
	if tokenUpper == "ETH" {
		// For native ETH, use the eth_decimals config
		configPath = "eth_decimals"
	} else {
		// For tokens like USDT, use the token-specific config
		configPath = fmt.Sprintf("depositCheck.chains.%s.tokens.%s.decimals", chainUpper, strings.ToLower(tokenUpper))
	}

	decimals, err := g.Cfg().Get(ctx, configPath)
	if err != nil {
		return 0, fmt.Errorf("failed to get %s decimals for %s from config: %w", tokenUpper, chainUpper, err)
	}

	if decimals.IsEmpty() {
		// Default to 18 decimals for ETH, 6 for USDT
		if tokenUpper == "ETH" {
			return 18, nil
		}
		return 6, nil
	}

	return decimals.Int(), nil
}

// GetConfirmations returns the number of confirmations required for a given token on Ethereum
func GetConfirmations(ctx context.Context, chain string, token string) (int, error) {
	// Normalize chain and token names to uppercase for consistency
	chainUpper := strings.ToUpper(chain)
	tokenUpper := strings.ToUpper(token)

	var configPath string
	if tokenUpper == "ETH" {
		// For native ETH, use the chain-level confirmations
		configPath = fmt.Sprintf("depositCheck.chains.%s.confirmations", chainUpper)
	} else {
		// For tokens like USDT, use the token-specific confirmations if available
		configPath = fmt.Sprintf("depositCheck.chains.%s.tokens.%s.confirmations", chainUpper, strings.ToLower(tokenUpper))

		// Check if token-specific confirmations exist
		confirmations, err := g.Cfg().Get(ctx, configPath)
		if err != nil || confirmations.IsEmpty() {
			// Fall back to chain-level confirmations
			configPath = fmt.Sprintf("depositCheck.chains.%s.confirmations", chainUpper)
		}
	}

	confirmations, err := g.Cfg().Get(ctx, configPath)
	if err != nil {
		return 0, fmt.Errorf("failed to get confirmations for %s on %s from config: %w", tokenUpper, chainUpper, err)
	}

	if confirmations.IsEmpty() {
		// Default to 6 confirmations if not specified (common for ETH)
		return 6, nil
	}

	return confirmations.Int(), nil
}
