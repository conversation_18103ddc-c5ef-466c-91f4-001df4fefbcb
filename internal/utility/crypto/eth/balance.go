package eth

import (
	"context"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

const (
	httpTimeout = 10 * time.Second
	maxRetries  = 3
	retryDelay  = 1 * time.Second
)

// GetETHBalance returns the ETH balance for a given address as a formatted string
func GetETHBalance(address string) (string, error) {
	ctx := context.Background()

	// Get HTTP RPC URL from config
	rpcUrl, err := GetEthHttpRpcUrl(ctx)
	if err != nil {
		return "0", fmt.Errorf("failed to get ETH HTTP RPC URL: %w", err)
	}

	// Get API key from config (if any)
	apiKey, err := GetEthApiKey(ctx)
	if err != nil {
		return "0", fmt.<PERSON>rrorf("failed to get ETH API key: %w", err)
	}

	// Create HTTP client
	client := &http.Client{Timeout: httpTimeout}

	// Get ETH balance
	balance, err := getEthBalance(ctx, client, rpcUrl, apiKey, address)
	if err != nil {
		return "0", fmt.Errorf("failed to get ETH balance: %w", err)
	}

	// Convert balance from *big.Int to decimal.Decimal (assumed to be in Wei)
	// The original code fetched decimals using GetTokenDecimals. However, the compiler error
	// "too many arguments in call to WeiToETH ... want (decimal.Decimal)"
	// implies WeiToETH expects a single decimal.Decimal argument (the Wei amount)
	// and internally knows ETH's decimals (e.g., 18).
	// Thus, the 'decimals' variable obtained from GetTokenDecimals is not used in this WeiToETH call.
	weiAmountDecimal := decimal.NewFromBigInt(balance, 0)

	// Call WeiToETH. Based on compiler errors, it expects one decimal.Decimal argument (Wei)
	// and returns a decimal.Decimal (ETH) and an error.
	ethBalanceDecimal, err := WeiToETH(weiAmountDecimal)
	if err != nil {
		return "0", fmt.Errorf("failed to convert Wei to ETH: %w", err)
	}

	// The function GetETHBalance returns a string, so convert the decimal.Decimal to string.
	return ethBalanceDecimal.String(), nil
}

// GetErc20UsdtBalance returns the ERC20 USDT balance for a given address as a formatted string
func GetErc20UsdtBalance(address string) (string, error) {
	ctx := context.Background()

	// Get HTTP RPC URL from config
	rpcUrl, err := GetEthHttpRpcUrl(ctx)
	if err != nil {
		return "0", fmt.Errorf("failed to get ETH HTTP RPC URL: %w", err)
	}

	// Get API key from config (if any)
	apiKey, err := GetEthApiKey(ctx)
	if err != nil {
		return "0", fmt.Errorf("failed to get ETH API key: %w", err)
	}

	// Get USDT contract address from config
	contractAddress, err := GetContractAddress(ctx, "ETH", "USDT")
	if err != nil {
		return "0", fmt.Errorf("failed to get USDT contract address: %w", err)
	}

	// Create HTTP client
	client := &http.Client{Timeout: httpTimeout}

	// Get ERC20 token balance
	balance, err := getErc20TokenBalance(ctx, client, rpcUrl, apiKey, address, contractAddress)
	if err != nil {
		return "0", fmt.Errorf("failed to get ERC20 token balance: %w", err)
	}

	// Get USDT decimals from config
	decimals, err := GetTokenDecimals(ctx, "ETH", "USDT")
	if err != nil {
		// Default to 6 decimals if not specified
		decimals = 6
	}

	// Format balance as token string
	formattedBalance := FormatTokenValue(balance.String(), decimals)

	return formattedBalance, nil
}

// GetAccountBalances returns both ETH and ERC20 USDT balances for a given address
func GetAccountBalances(address string) (string, map[string]string, error) {
	ctx := context.Background()

	// Get ETH balance
	ethBalance, err := GetETHBalance(address)
	if err != nil {
		return "0", nil, fmt.Errorf("failed to get ETH balance: %w", err)
	}

	// Get USDT contract address from config
	usdtContractAddress, err := GetContractAddress(ctx, "ETH", "USDT")
	if err != nil {
		// Return only ETH balance if USDT contract address is not available
		return ethBalance, map[string]string{}, nil
	}

	// Get ERC20 USDT balance
	usdtBalance, err := GetErc20UsdtBalance(address)
	if err != nil {
		// Return only ETH balance if USDT balance query fails
		g.Log().Warningf(ctx, "Failed to get ERC20 USDT balance for address %s: %v", address, err)
		return ethBalance, map[string]string{}, nil
	}

	// Return both balances
	erc20Balances := map[string]string{
		usdtContractAddress: usdtBalance,
	}

	return ethBalance, erc20Balances, nil
}

// getEthBalance retrieves ETH balance from the Ethereum network
func getEthBalance(ctx context.Context, client *http.Client, rpcUrl, apiKey, address string) (*big.Int, error) {
	// Ensure address is valid
	if !common.IsHexAddress(address) {
		return nil, fmt.Errorf("invalid Ethereum address: %s", address)
	}

	// Normalize address
	normalizedAddr := common.HexToAddress(address).Hex()

	// Prepare JSON-RPC request
	requestBody := fmt.Sprintf(`{
		"jsonrpc": "2.0",
		"method": "eth_getBalance",
		"params": ["%s", "latest"],
		"id": 1
	}`, normalizedAddr)

	// Create request
	req, err := http.NewRequestWithContext(ctx, "POST", rpcUrl, strings.NewReader(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	if apiKey != "" {
		req.Header.Set("Authorization", "Bearer "+apiKey)
	}
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	// Send request with retries
	var respData map[string]any
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		resp, err := client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("attempt %d: failed to execute request: %w", attempt+1, err)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(retryDelay)
			continue
		}

		bodyBytes, readErr := io.ReadAll(resp.Body)
		resp.Body.Close()
		if readErr != nil {
			lastErr = fmt.Errorf("attempt %d: failed to read response body: %w", attempt+1, readErr)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(retryDelay)
			continue
		}

		if resp.StatusCode != http.StatusOK {
			lastErr = fmt.Errorf("attempt %d: API returned non-OK status: %d. Body: %s", attempt+1, resp.StatusCode, string(bodyBytes))
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(retryDelay)
			continue
		}

		// Parse response
		if err := gjson.Unmarshal(bodyBytes, &respData); err != nil {
			lastErr = fmt.Errorf("attempt %d: failed to decode response: %w. Body: %s", attempt+1, err, string(bodyBytes))
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(retryDelay)
			continue
		}

		// Check for JSON-RPC error
		if errObj, ok := respData["error"]; ok {
			errMap, ok := errObj.(map[string]any)
			if ok {
				errMsg := fmt.Sprintf("JSON-RPC error: code=%v, message=%v", errMap["code"], errMap["message"])
				lastErr = fmt.Errorf("attempt %d: %s", attempt+1, errMsg)
				g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
				time.Sleep(retryDelay)
				continue
			}
		}

		// Extract balance from response
		result, ok := respData["result"].(string)
		if !ok {
			lastErr = fmt.Errorf("attempt %d: invalid response format, missing or invalid 'result'", attempt+1)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(retryDelay)
			continue
		}

		// Convert hex to big.Int
		balance := new(big.Int)
		balance.SetString(strings.TrimPrefix(result, "0x"), 16)

		return balance, nil // Success
	}

	g.Log().Errorf(ctx, "Failed to get ETH balance after %d retries: %v", maxRetries, lastErr)
	return nil, lastErr
}

// getErc20TokenBalance retrieves ERC20 token balance from the Ethereum network
func getErc20TokenBalance(ctx context.Context, client *http.Client, rpcUrl, apiKey, address, contractAddress string) (*big.Int, error) {
	// Ensure addresses are valid
	if !common.IsHexAddress(address) {
		return nil, fmt.Errorf("invalid Ethereum address: %s", address)
	}
	if !common.IsHexAddress(contractAddress) {
		return nil, fmt.Errorf("invalid contract address: %s", contractAddress)
	}

	// Normalize addresses
	normalizedAddr := common.HexToAddress(address).Hex()
	normalizedContract := common.HexToAddress(contractAddress).Hex()

	// Create ERC20 balanceOf function call data
	// balanceOf function signature: 0x70a08231
	// Pad address to 32 bytes (64 hex chars) for ABI encoding
	addrWithoutPrefix := strings.TrimPrefix(normalizedAddr, "0x")
	paddedAddr := fmt.Sprintf("%064s", addrWithoutPrefix)
	data := "0x70a08231" + paddedAddr

	// Prepare JSON-RPC request
	requestBody := fmt.Sprintf(`{
		"jsonrpc": "2.0",
		"method": "eth_call",
		"params": [{"to": "%s", "data": "%s"}, "latest"],
		"id": 1
	}`, normalizedContract, data)

	// Create request
	req, err := http.NewRequestWithContext(ctx, "POST", rpcUrl, strings.NewReader(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	if apiKey != "" {
		req.Header.Set("Authorization", "Bearer "+apiKey)
	}
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	// Send request with retries
	var respData map[string]any
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		resp, err := client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("attempt %d: failed to execute request: %w", attempt+1, err)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(retryDelay)
			continue
		}

		bodyBytes, readErr := io.ReadAll(resp.Body)
		resp.Body.Close()
		if readErr != nil {
			lastErr = fmt.Errorf("attempt %d: failed to read response body: %w", attempt+1, readErr)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(retryDelay)
			continue
		}

		if resp.StatusCode != http.StatusOK {
			lastErr = fmt.Errorf("attempt %d: API returned non-OK status: %d. Body: %s", attempt+1, resp.StatusCode, string(bodyBytes))
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(retryDelay)
			continue
		}

		// Parse response
		if err := gjson.Unmarshal(bodyBytes, &respData); err != nil {
			lastErr = fmt.Errorf("attempt %d: failed to decode response: %w. Body: %s", attempt+1, err, string(bodyBytes))
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(retryDelay)
			continue
		}

		// Check for JSON-RPC error
		if errObj, ok := respData["error"]; ok {
			errMap, ok := errObj.(map[string]any)
			if ok {
				errMsg := fmt.Sprintf("JSON-RPC error: code=%v, message=%v", errMap["code"], errMap["message"])
				lastErr = fmt.Errorf("attempt %d: %s", attempt+1, errMsg)
				g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
				time.Sleep(retryDelay)
				continue
			}
		}

		// Extract result from response
		result, ok := respData["result"].(string)
		if !ok {
			lastErr = fmt.Errorf("attempt %d: invalid response format, missing or invalid 'result'", attempt+1)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(retryDelay)
			continue
		}

		// Convert hex to big.Int
		balance := new(big.Int)
		balance.SetString(strings.TrimPrefix(result, "0x"), 16)

		return balance, nil // Success
	}

	g.Log().Errorf(ctx, "Failed to get ERC20 token balance after %d retries: %v", maxRetries, lastErr)
	return nil, lastErr
}
