package crypto

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"math/big"
	"strings"
	"time"

	"task-withdraw/internal/utility/utils/bip39"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
	"golang.org/x/crypto/sha3"
)

// ETHClient implements BlockchainClient for Ethereum
type ETHClient struct {
	client *ethclient.Client
	config *ClientConfig
}

// NewETHClient creates a new ETH client
func NewETHClient(config *ClientConfig) (*ETHClient, error) {
	if config == nil || config.RPCUrl == "" {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"ETH RPC URL is required",
			ChainETH,
		)
	}

	client, err := ethclient.Dial(config.RPCUrl)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to connect to ETH RPC",
			ChainETH,
			map[string]interface{}{"rpc_url": config.RPCUrl, "error": err.Error()},
		)
	}

	return &ETHClient{
		client: client,
		config: config,
	}, nil
}

// GetChainType returns the chain type
func (c *ETHClient) GetChainType() ChainType {
	return ChainETH
}

// GetChainID returns the chain ID
func (c *ETHClient) GetChainID(ctx context.Context) (*big.Int, error) {
	chainID, err := c.client.ChainID(ctx)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to get chain ID",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}
	return chainID, nil
}

// GenerateAddress generates an address from private key
func (c *ETHClient) GenerateAddress(privateKey *ecdsa.PrivateKey) (*Address, error) {
	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return nil, NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"failed to cast public key to ECDSA",
			ChainETH,
		)
	}

	address := crypto.PubkeyToAddress(*publicKeyECDSA)

	return &Address{
		Address:   address.Hex(),
		ChainType: ChainETH,
		CreatedAt: time.Now(),
	}, nil
}

// ValidateAddress validates an Ethereum address
func (c *ETHClient) ValidateAddress(address string) bool {
	return common.IsHexAddress(address)
}

// ValidatePrivateKey validates a private key
func (c *ETHClient) ValidatePrivateKey(privateKey string) bool {
	privateKey = strings.TrimPrefix(privateKey, "0x")
	if len(privateKey) != 64 {
		return false
	}

	_, err := crypto.HexToECDSA(privateKey)
	return err == nil
}

// GetAddressFromPrivateKey gets address from private key string
func (c *ETHClient) GetAddressFromPrivateKey(privateKeyStr string) (string, error) {
	privateKeyStr = strings.TrimPrefix(privateKeyStr, "0x")
	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"invalid private key format",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"failed to cast public key to ECDSA",
			ChainETH,
		)
	}

	address := crypto.PubkeyToAddress(*publicKeyECDSA)
	return address.Hex(), nil
}

// GenerateMnemonic generates a new mnemonic phrase
func (c *ETHClient) GenerateMnemonic() (string, error) {
	entropy, err := bip39.NewEntropy(128) // 128 bits = 12 words
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"failed to generate entropy",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}

	mnemonic, err := bip39.NewMnemonic(entropy)
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"failed to generate mnemonic",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}

	return mnemonic, nil
}

// ValidateMnemonic validates a mnemonic phrase
func (c *ETHClient) ValidateMnemonic(mnemonic string) bool {
	return bip39.IsMnemonicValid(mnemonic)
}

// GeneratePrivateKey generates a new private key
func (c *ETHClient) GeneratePrivateKey() (string, error) {
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"failed to generate private key",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}

	return "0x" + common.Bytes2Hex(crypto.FromECDSA(privateKey)), nil
}

// GetDerivedPath returns the derivation path for given index
func (c *ETHClient) GetDerivedPath(index int) string {
	return fmt.Sprintf("m/44'/60'/0'/0/%d", index)
}

// DeriveKeyFromPath derives a private key from mnemonic and path
func (c *ETHClient) DeriveKeyFromPath(mnemonic string, path string) (*ecdsa.PrivateKey, error) {
	// This would use the BIP32 implementation
	// For now, we'll use the existing HD derivation functions
	return nil, NewBlockchainError(
		ErrCodeUnsupportedOperation,
		"HD key derivation not implemented yet",
		ChainETH,
	)
}

// GetNativeBalance gets ETH balance
func (c *ETHClient) GetNativeBalance(ctx context.Context, address string) (*Balance, error) {
	if !c.ValidateAddress(address) {
		return nil, NewBlockchainError(
			ErrCodeInvalidAddress,
			"invalid Ethereum address",
			ChainETH,
			map[string]interface{}{"address": address},
		)
	}

	account := common.HexToAddress(address)
	balanceWei, err := c.client.BalanceAt(ctx, account, nil)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to get ETH balance",
			ChainETH,
			map[string]interface{}{"address": address, "error": err.Error()},
		)
	}

	// Convert from Wei to ETH (1 ETH = 10^18 Wei)
	balance := decimal.NewFromBigInt(balanceWei, -18)

	return &Balance{
		Address:     address,
		ChainType:   ChainETH,
		NativeToken: balance,
		TokenSymbol: "ETH",
		UpdatedAt:   time.Now(),
	}, nil
}

// GetTokenBalance gets ERC20 token balance
func (c *ETHClient) GetTokenBalance(ctx context.Context, address string, contractAddress string) (*Balance, error) {
	if !c.ValidateAddress(address) {
		return nil, NewBlockchainError(
			ErrCodeInvalidAddress,
			"invalid Ethereum address",
			ChainETH,
			map[string]interface{}{"address": address},
		)
	}

	if !c.ValidateAddress(contractAddress) {
		return nil, NewBlockchainError(
			ErrCodeInvalidAddress,
			"invalid contract address",
			ChainETH,
			map[string]interface{}{"contract_address": contractAddress},
		)
	}

	// ERC20 balanceOf function signature
	balanceOfSignature := []byte("balanceOf(address)")
	hash := sha3.NewLegacyKeccak256()
	hash.Write(balanceOfSignature)
	methodID := hash.Sum(nil)[:4]

	// Pad address to 32 bytes
	account := common.HexToAddress(address)
	paddedAddress := common.LeftPadBytes(account.Bytes(), 32)

	// Construct call data
	var data []byte
	data = append(data, methodID...)
	data = append(data, paddedAddress...)

	// Call contract
	contractAddr := common.HexToAddress(contractAddress)
	result, err := c.client.CallContract(ctx, ethereum.CallMsg{
		To:   &contractAddr,
		Data: data,
	}, nil)

	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeContractError,
			"failed to call ERC20 balanceOf",
			ChainETH,
			map[string]interface{}{
				"address":  address,
				"contract": contractAddress,
				"error":    err.Error(),
			},
		)
	}

	// Parse result
	balanceTokenUnits := new(big.Int).SetBytes(result)

	// Convert from token units to human readable (assuming 6 decimals for USDT)
	balance := decimal.NewFromBigInt(balanceTokenUnits, -6)

	return &Balance{
		Address:      address,
		ChainType:    ChainETH,
		TokenBalance: balance,
		TokenSymbol:  "USDT",
		UpdatedAt:    time.Now(),
	}, nil
}

// GetAllBalances gets both ETH and token balances
func (c *ETHClient) GetAllBalances(ctx context.Context, address string) (*Balance, error) {
	ethBalance, err := c.GetNativeBalance(ctx, address)
	if err != nil {
		return nil, err
	}

	// Get default contract address for token balance
	tokenBalance := decimal.Zero
	if c.config.ContractAddress != "" {
		tokenBal, err := c.GetTokenBalance(ctx, address, c.config.ContractAddress)
		if err == nil {
			tokenBalance = tokenBal.TokenBalance
		}
	}

	return &Balance{
		Address:      address,
		ChainType:    ChainETH,
		NativeToken:  ethBalance.NativeToken,
		TokenBalance: tokenBalance,
		TokenSymbol:  "USDT",
		UpdatedAt:    time.Now(),
	}, nil
}

// SendTransaction sends a transaction
func (c *ETHClient) SendTransaction(ctx context.Context, req *SendTransactionRequest) (*Transaction, error) {
	// Parse private key
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(req.PrivateKey, "0x"))
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"invalid private key",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}

	// Get sender address
	fromAddress := crypto.PubkeyToAddress(privateKey.PublicKey)

	// Get nonce
	nonce, err := c.client.PendingNonceAt(ctx, fromAddress)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to get nonce",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}

	// Get gas price
	gasPrice := big.NewInt(20000000000) // Default 20 Gwei
	if req.GasPrice != nil {
		gasPrice = req.GasPrice
	} else {
		suggestedPrice, err := c.client.SuggestGasPrice(ctx)
		if err == nil {
			gasPrice = suggestedPrice
		}
	}

	var tx *types.Transaction

	switch req.TokenType {
	case TokenNative:
		// ETH transfer
		value := req.Amount.Mul(decimal.NewFromInt(1e18)).BigInt() // Convert to Wei

		gasLimit := uint64(21000)
		if req.GasLimit != nil {
			gasLimit = req.GasLimit.Uint64()
		}

		tx = types.NewTransaction(nonce, common.HexToAddress(req.ToAddress), value, gasLimit, gasPrice, nil)

	case TokenERC20:
		// ERC20 transfer
		if req.ContractAddress == "" {
			req.ContractAddress = c.config.ContractAddress
		}

		if req.ContractAddress == "" {
			return nil, NewBlockchainError(
				ErrCodeInvalidAddress,
				"contract address is required for ERC20 transfer",
				ChainETH,
			)
		}

		// Prepare ERC20 transfer data
		transferSignature := []byte("transfer(address,uint256)")
		hash := sha3.NewLegacyKeccak256()
		hash.Write(transferSignature)
		methodID := hash.Sum(nil)[:4]

		toAddress := common.HexToAddress(req.ToAddress)
		paddedAddress := common.LeftPadBytes(toAddress.Bytes(), 32)

		// Convert amount to token units (assuming 6 decimals for USDT)
		amount := req.Amount.Mul(decimal.NewFromInt(1e6)).BigInt()
		paddedAmount := common.LeftPadBytes(amount.Bytes(), 32)

		var data []byte
		data = append(data, methodID...)
		data = append(data, paddedAddress...)
		data = append(data, paddedAmount...)

		gasLimit := uint64(60000)
		if req.GasLimit != nil {
			gasLimit = req.GasLimit.Uint64()
		}

		tx = types.NewTransaction(nonce, common.HexToAddress(req.ContractAddress), big.NewInt(0), gasLimit, gasPrice, data)

	default:
		return nil, NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"unsupported token type for ETH",
			ChainETH,
		)
	}

	// Get chain ID
	chainID, err := c.client.NetworkID(ctx)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to get chain ID",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}

	// Sign transaction
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeTransactionFailed,
			"failed to sign transaction",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}

	// Send transaction
	err = c.client.SendTransaction(ctx, signedTx)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeTransactionFailed,
			"failed to send transaction",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}

	return &Transaction{
		Hash:            signedTx.Hash().Hex(),
		ChainType:       ChainETH,
		TokenType:       req.TokenType,
		FromAddress:     fromAddress.Hex(),
		ToAddress:       req.ToAddress,
		Amount:          req.Amount,
		ContractAddress: req.ContractAddress,
		Status:          TxStatusPending,
		CreatedAt:       time.Now(),
	}, nil
}

// EstimateFee estimates transaction fee
func (c *ETHClient) EstimateFee(ctx context.Context, req *SendTransactionRequest) (*FeeEstimate, error) {
	// Get current gas price
	gasPrice, err := c.client.SuggestGasPrice(ctx)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to get gas price",
			ChainETH,
			map[string]interface{}{"error": err.Error()},
		)
	}

	var gasLimit *big.Int
	switch req.TokenType {
	case TokenNative:
		gasLimit = big.NewInt(21000) // Standard ETH transfer
	case TokenERC20:
		gasLimit = big.NewInt(60000) // Standard ERC20 transfer
	default:
		return nil, NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"unsupported token type for fee estimation",
			ChainETH,
		)
	}

	// Calculate fee: gasLimit * gasPrice
	feeWei := new(big.Int).Mul(gasLimit, gasPrice)
	feeEth := decimal.NewFromBigInt(feeWei, -18) // Convert Wei to ETH

	return &FeeEstimate{
		ChainType:    ChainETH,
		TokenType:    req.TokenType,
		EstimatedFee: feeEth,
		GasLimit:     gasLimit,
		GasPrice:     gasPrice,
		EstimatedAt:  time.Now(),
	}, nil
}

// GetTransaction gets transaction by hash
func (c *ETHClient) GetTransaction(ctx context.Context, hash string) (*Transaction, error) {
	// This would implement transaction lookup
	return nil, NewBlockchainError(
		ErrCodeUnsupportedOperation,
		"get transaction not implemented yet",
		ChainETH,
	)
}

// GetTransactionStatus gets transaction status
func (c *ETHClient) GetTransactionStatus(ctx context.Context, hash string) (TxStatus, error) {
	// This would implement transaction status lookup
	return TxStatusPending, NewBlockchainError(
		ErrCodeUnsupportedOperation,
		"get transaction status not implemented yet",
		ChainETH,
	)
}

// FormatAmount formats amount for display
func (c *ETHClient) FormatAmount(amount decimal.Decimal, tokenType TokenType) string {
	switch tokenType {
	case TokenNative:
		return amount.String() + " ETH"
	case TokenERC20:
		return amount.String() + " USDT"
	default:
		return amount.String()
	}
}

// ParseAmount parses amount string to decimal
func (c *ETHClient) ParseAmount(amountStr string, tokenType TokenType) (decimal.Decimal, error) {
	amount, err := decimal.NewFromString(amountStr)
	if err != nil {
		return decimal.Zero, NewBlockchainError(
			ErrCodeInvalidAddress,
			"invalid amount format",
			ChainETH,
			map[string]interface{}{"amount": amountStr, "error": err.Error()},
		)
	}
	return amount, nil
}

// GetFeeAddress gets fee address for private key
func (c *ETHClient) GetFeeAddress(privateKey string) (string, error) {
	return c.GetAddressFromPrivateKey(privateKey)
}

// Close closes the ETH client
func (c *ETHClient) Close() error {
	if c.client != nil {
		c.client.Close()
	}
	return nil
}
