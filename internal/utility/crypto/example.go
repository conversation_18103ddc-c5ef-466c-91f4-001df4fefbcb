package crypto

import (
	"context"
	"fmt"
	"log"

	"github.com/shopspring/decimal"
)

// ExampleUsage demonstrates how to use the new unified crypto interface
func ExampleUsage() {
	ctx := context.Background()

	// Example 1: Get supported chains
	fmt.Println("Supported chains:", SupportedChains())

	// Example 2: Validate addresses
	ethAddress := "0x742d35Cc1234567890123456789012345678901234"
	tronAddress := "TLyqzVGLV1srkB7dToTAEqgDSfPtXRJZYH"

	fmt.Printf("ETH address valid: %v\n", ValidateETHAddress(ethAddress))
	fmt.Printf("TRON address valid: %v\n", ValidateTRONAddress(tronAddress))

	// Example 3: Generate private keys
	ethPrivateKey, err := GeneratePrivateKey(ChainETH)
	if err != nil {
		log.Printf("Failed to generate ETH private key: %v", err)
	} else {
		fmt.Printf("Generated ETH private key: %s\n", ethPrivateKey)
	}

	tronPrivateKey, err := GeneratePrivateKey(ChainTRON)
	if err != nil {
		log.Printf("Failed to generate TRON private key: %v", err)
	} else {
		fmt.Printf("Generated TRON private key: %s\n", tronPrivateKey)
	}

	// Example 4: Generate addresses from private keys
	if ethPrivateKey != "" {
		ethAddr, err := GenerateETHAddress(ethPrivateKey)
		if err != nil {
			log.Printf("Failed to generate ETH address: %v", err)
		} else {
			fmt.Printf("Generated ETH address: %s\n", ethAddr)
		}
	}

	if tronPrivateKey != "" {
		tronAddr, err := GenerateTRONAddress(tronPrivateKey)
		if err != nil {
			log.Printf("Failed to generate TRON address: %v", err)
		} else {
			fmt.Printf("Generated TRON address: %s\n", tronAddr)
		}
	}

	// Example 5: Get balances
	// Get ETH balance
	ethBalance, err := GetETHBalance(ctx, ethAddress)
	if err != nil {
		log.Printf("Failed to get ETH balance: %v", err)
	} else {
		fmt.Printf("ETH balance: %s ETH\n", ethBalance.NativeToken.String())
	}

	// Get TRX balance
	trxBalance, err := GetTRXBalance(ctx, tronAddress)
	if err != nil {
		log.Printf("Failed to get TRX balance: %v", err)
	} else {
		fmt.Printf("TRX balance: %s TRX\n", trxBalance.NativeToken.String())
	}

	// Example 6: Send transactions (commented out to avoid accidental execution)
	/*
	// Send ETH
	amount := decimal.NewFromFloat(0.001) // 0.001 ETH
	toAddress := "******************************************"
	
	tx, err := SendETH(ctx, ethPrivateKey, toAddress, amount)
	if err != nil {
		log.Printf("Failed to send ETH: %v", err)
	} else {
		fmt.Printf("ETH transaction hash: %s\n", tx.Hash)
	}

	// Send TRX
	amount = decimal.NewFromFloat(1.0) // 1 TRX
	toAddress = "TLyqzVGLV1srkB7dToTAEqgDSfPtXRJZYH"
	
	tx, err = SendTRX(ctx, tronPrivateKey, toAddress, amount)
	if err != nil {
		log.Printf("Failed to send TRX: %v", err)
	} else {
		fmt.Printf("TRX transaction hash: %s\n", tx.Hash)
	}
	*/

	// Example 7: Estimate fees
	if ethPrivateKey != "" {
		req := NewETHTransferRequest(ethPrivateKey, ethAddress, decimal.NewFromFloat(0.001))
		fee, err := EstimateFee(ctx, ChainETH, req)
		if err != nil {
			log.Printf("Failed to estimate ETH fee: %v", err)
		} else {
			fmt.Printf("Estimated ETH fee: %s ETH\n", fee.EstimatedFee.String())
		}
	}

	if tronPrivateKey != "" {
		req := NewTRXTransferRequest(tronPrivateKey, tronAddress, decimal.NewFromFloat(1.0))
		fee, err := EstimateFee(ctx, ChainTRON, req)
		if err != nil {
			log.Printf("Failed to estimate TRX fee: %v", err)
		} else {
			fmt.Printf("Estimated TRX fee: %s TRX\n", fee.EstimatedFee.String())
		}
	}
}

// ExampleAdvancedUsage demonstrates advanced usage patterns
func ExampleAdvancedUsage() {
	ctx := context.Background()

	// Example 1: Direct manager usage
	manager := DefaultManager()
	
	// Get ETH client directly
	ethClient, err := manager.GetClient(ChainETH)
	if err != nil {
		log.Printf("Failed to get ETH client: %v", err)
		return
	}

	// Generate a mnemonic
	mnemonic, err := ethClient.GenerateMnemonic()
	if err != nil {
		log.Printf("Failed to generate mnemonic: %v", err)
	} else {
		fmt.Printf("Generated mnemonic: %s\n", mnemonic)
	}

	// Example 2: Configuration management
	if mgr, ok := manager.(*Manager); ok {
		ethConfig, err := mgr.GetConfig(ChainETH)
		if err != nil {
			log.Printf("Failed to get ETH config: %v", err)
		} else {
			fmt.Printf("ETH RPC URL: %s\n", ethConfig.RPCUrl)
			fmt.Printf("ETH Contract Address: %s\n", ethConfig.ContractAddress)
		}
	}

	// Example 3: Custom transaction requests
	privateKey := "your_private_key_here"
	toAddress := "******************************************"
	contractAddress := "******************************************" // Example USDT contract
	amount := decimal.NewFromFloat(10.0) // 10 USDT

	// Create custom ERC20 transfer request
	req := &SendTransactionRequest{
		PrivateKey:      privateKey,
		ToAddress:       toAddress,
		Amount:          amount,
		TokenType:       TokenERC20,
		ContractAddress: contractAddress,
		LogPrefix:       "ERC20_Transfer",
	}

	// Send the transaction (commented out to avoid accidental execution)
	_ = req // Use the variable to avoid unused warning
	/*
	tx, err := manager.SendTransaction(ctx, ChainETH, req)
	if err != nil {
		log.Printf("Failed to send ERC20: %v", err)
	} else {
		fmt.Printf("ERC20 transaction hash: %s\n", tx.Hash)
	}
	*/

	// Example 4: Error handling with blockchain-specific errors
	_, err = GetBalance(ctx, ChainETH, "invalid_address", TokenNative)
	if err != nil {
		if blockchainErr, ok := err.(*BlockchainError); ok {
			fmt.Printf("Blockchain error code: %s\n", blockchainErr.Code)
			fmt.Printf("Blockchain error message: %s\n", blockchainErr.Message)
			fmt.Printf("Chain type: %s\n", blockchainErr.ChainType)
		}
	}
}

// ExampleMigrationFromOldInterface shows how to migrate from old interface
func ExampleMigrationFromOldInterface() {
	ctx := context.Background()

	// Old way (still supported but deprecated)
	ethRpcUrl, _ := GetEthRpcUrl(ctx)
	fmt.Printf("ETH RPC URL (old way): %s\n", ethRpcUrl)

	// New way (recommended)
	manager := DefaultManager()
	if mgr, ok := manager.(*Manager); ok {
		config, err := mgr.GetConfig(ChainETH)
		if err == nil {
			fmt.Printf("ETH RPC URL (new way): %s\n", config.RPCUrl)
		}
	}

	// Old balance checking (if available in existing code)
	// ethBalance := GetETHBalanceOldWay(address)

	// New balance checking
	address := "0x742d35Cc1234567890123456789012345678901234"
	balance, err := GetETHBalance(ctx, address)
	if err == nil {
		fmt.Printf("ETH balance: %s\n", balance.NativeToken.String())
	}
}