# Merchant Withdrawals and Settlements Integration Plan

## Overview

This document outlines the comprehensive plan for integrating merchant withdrawals and settlements into the existing task-withdraw client system. The integration follows the established patterns while extending the architecture to handle merchant-specific operations.

## 1. Updated Client API ✅

The client API has been successfully updated with the new merchant gRPC methods:

### New Methods Available:
- `ListMerchantWithdraws` - List merchant withdrawal records with filtering and pagination
- `UpdateMerchantWithdraw` - Update merchant withdrawal status and fields
- `ListMerchantSettlements` - List merchant settlement records with filtering and pagination  
- `UpdateMerchantSettlement` - Update merchant settlement status and fields

### New Message Types:
- `MerchantWithdraw` - Merchant withdrawal record structure
- `MerchantSettlement` - Merchant settlement record structure
- Request/Response messages for all operations

## 2. Architecture Integration Strategy

### 2.1 Current Architecture Analysis

The existing system follows a producer-consumer pattern:

```
[gRPC Server] → [Withdrawal Processor] → [Redis Queue] → [Withdrawal Consumer] → [Blockchain Senders]
                      ↓                                        ↓
                [Status Updates] ← [gRPC Status Updater] ← [Status Queue]
```

### 2.2 Proposed Merchant Integration

The merchant integration will extend this architecture while maintaining consistency:

```
[gRPC Server] → [Enhanced Processor] → [Unified Redis Queue] → [Enhanced Consumer] → [Blockchain Senders]
                      ↓                                           ↓
                [Status Updates] ← [Enhanced gRPC Updater] ← [Status Queue]
```

## 3. Component Modifications Required

### 3.1 Service Interface Extension

**File**: `internal/service/withdrawal_client.go`

**Current Interface**:
```go
type IWithdrawalClient interface {
    FetchPendingWithdrawals(ctx context.Context, batchSize int) ([]*taskv1.Withdrawal, error)
    UpdateWithdrawalStatus(ctx context.Context, req *taskv1.UpdateWithdrawalStatusRequest) error
    Close() error
}
```

**Enhanced Interface**:
```go
type IWithdrawalClient interface {
    // Existing user withdrawal methods
    FetchPendingWithdrawals(ctx context.Context, batchSize int) ([]*taskv1.Withdrawal, error)
    UpdateWithdrawalStatus(ctx context.Context, req *taskv1.UpdateWithdrawalStatusRequest) error
    
    // New merchant withdrawal methods
    FetchPendingMerchantWithdrawals(ctx context.Context, batchSize int) ([]*taskv1.MerchantWithdraw, error)
    UpdateMerchantWithdrawStatus(ctx context.Context, req *taskv1.UpdateMerchantWithdrawRequest) error
    
    // New merchant settlement methods
    FetchPendingMerchantSettlements(ctx context.Context, batchSize int) ([]*taskv1.MerchantSettlement, error)
    UpdateMerchantSettlementStatus(ctx context.Context, req *taskv1.UpdateMerchantSettlementRequest) error
    
    Close() error
}
```

### 3.2 gRPC Client Implementation

**File**: `internal/client/task/client.go`

**Required Changes**:
1. Add merchant-specific methods to `WithdrawalClient` struct
2. Implement fetching logic for merchant withdrawals and settlements
3. Implement status update methods for merchant operations

### 3.3 Withdrawal Processor Enhancement

**File**: `internal/logic/task/withdrawal_processor/processor.go`

**Current Function**: `ProcessPendingWithdrawals`

**Enhancement Strategy**:
1. **Option A: Unified Processor** - Extend existing processor to handle all transaction types
2. **Option B: Separate Processors** - Create dedicated processors for each type
3. **Option C: Configurable Processor** - Single processor with type-based configuration

**Recommended: Option A - Unified Processor**

**New Function Structure**:
```go
func ProcessPendingTransactions(ctx context.Context) {
    // Process user withdrawals
    processUserWithdrawals(ctx)
    
    // Process merchant withdrawals  
    processMerchantWithdrawals(ctx)
    
    // Process merchant settlements
    processMerchantSettlements(ctx)
}
```

### 3.4 Message Structure Enhancement

**Current Message**: `WithdrawalRequest` (user withdrawals only)

**Enhanced Message Structure**:
```go
type TransactionRequest struct {
    Type string `json:"type"` // "user_withdrawal", "merchant_withdrawal", "merchant_settlement"
    
    // Union field - only one will be populated based on Type
    UserWithdrawal     *taskv1.Withdrawal        `json:"user_withdrawal,omitempty"`
    MerchantWithdrawal *taskv1.MerchantWithdraw  `json:"merchant_withdrawal,omitempty"`
    MerchantSettlement *taskv1.MerchantSettlement `json:"merchant_settlement,omitempty"`
    
    // Common metadata
    EnqueuedAt time.Time `json:"enqueued_at"`
    Retries    int       `json:"retries"`
}
```

## 4. Processing Pipeline Modifications

### 4.1 Consumer Enhancement

**File**: `internal/logic/task/withdrawal_consumer/processor.go`

**Current**: `ProcessMessage` handles only user withdrawals

**Enhancement**: Type-based message routing

```go
func ProcessMessage(ctx context.Context, message string, ...) {
    var req TransactionRequest
    if err := json.Unmarshal([]byte(message), &req); err != nil {
        // Handle error
        return
    }
    
    switch req.Type {
    case "user_withdrawal":
        processUserWithdrawal(ctx, req.UserWithdrawal, ...)
    case "merchant_withdrawal":
        processMerchantWithdrawal(ctx, req.MerchantWithdrawal, ...)
    case "merchant_settlement":
        processMerchantSettlement(ctx, req.MerchantSettlement, ...)
    default:
        // Handle unknown type
    }
}
```

### 4.2 Transaction Processing Logic

**Shared Processing Steps** (applicable to all transaction types):
1. Derive transaction info (symbol, chain, token standard)
2. Get token configuration
3. Validate amount and limits
4. Select blockchain sender
5. Prepare transaction parameters
6. Send transaction
7. Handle result and update status

**Type-Specific Considerations**:
- **User Withdrawals**: Existing logic (no changes)
- **Merchant Withdrawals**: Similar to user withdrawals but with merchant-specific validation
- **Merchant Settlements**: May have different business rules and validation logic

## 5. Configuration Changes

### 5.1 Enhanced Configuration Structure

**File**: `manifest/config/config.yaml`

**New Configuration Sections**:
```yaml
# Enhanced withdrawal processor configuration
withdrawalProcessor:
  enabled: true
  spec: "*/10 * * * * *"
  batchSize: 50
  
  # Transaction type processing configuration
  transactionTypes:
    userWithdrawals:
      enabled: true
      batchSize: 50
    merchantWithdrawals:
      enabled: true
      batchSize: 30
    merchantSettlements:
      enabled: true
      batchSize: 20
  
  # Existing configuration...
  enabledTokens:
    ETH: true
    USDT_ERC20: true
    TRX: true
    USDT_TRC20: true
```

### 5.2 Queue Configuration

**Enhanced Queue Structure**:
```yaml
withdrawalConsumer:
  enabled: true
  # Unified queue for all transaction types
  redisQueueName: "queue:transaction_processing"
  concurrency: 1
  dlqName: "queue:transaction_processing_dlq"

grpcUpdater:
  # Unified status update queue
  redisQueueName: "queue:transaction_status_update"
  redisDlqName: "queue:transaction_status_update_dlq"
  brpopTimeoutSeconds: 5
  maxRetries: 5
  retryDelaySeconds: 10
```

## 6. Database Considerations

### 6.1 Merchant Tables Structure

The merchant tables (`merchant_withdraws` and `merchant_settlements`) have similar structures to user withdrawals but with key differences:

**Key Differences**:
- Primary key: `withdraws_id` / `settlements_id` (vs `user_withdraws_id`)
- Foreign key: `merchant_id` (vs `user_id`)
- Additional fields: `withdraws_type`/`settlements_type`, `notification_sent`, `fiat_type`
- State management: Single `state` field (vs multiple status fields)

### 6.2 Transaction ID Mapping

**Challenge**: Different primary key names across tables
**Solution**: Unified transaction identifier in processing logic

```go
type TransactionIdentifier struct {
    Type string // "user_withdrawal", "merchant_withdrawal", "merchant_settlement"
    ID   uint64 // The actual ID from the respective table
}
```

## 7. Error Handling and Logging

### 7.1 Enhanced Error Handling

**Type-Specific Error Handling**:
- Maintain existing error categorization (retryable vs non-retryable)
- Add transaction type context to all error messages
- Implement type-specific retry policies if needed

**Enhanced Logging**:
```go
logPrefix := fmt.Sprintf("[%s][ID:%d][OrderNo:%s]", 
    transactionType, transactionID, orderNo)
```

### 7.2 Status Update Enhancement

**File**: `internal/logic/task/grpc_updater/processor.go`

**Enhanced Status Update Message**:
```go
type StatusUpdateMessage struct {
    Type           string `json:"type"`
    TransactionID  uint64 `json:"transaction_id"`
    Status         int32  `json:"status"`
    TxHash         string `json:"tx_hash,omitempty"`
    ErrorMessage   string `json:"error_message,omitempty"`
    // ... other fields
}
```

## 8. Implementation Phases

### Phase 1: Foundation (Week 1)
1. ✅ Update protobuf definitions and generate client API
2. Extend service interfaces
3. Enhance gRPC client implementation
4. Update configuration structure

### Phase 2: Core Processing (Week 2)
1. Implement unified transaction message structure
2. Enhance withdrawal processor for multi-type support
3. Update consumer processing logic
4. Implement type-based routing

### Phase 3: Integration & Testing (Week 3)
1. Enhance status updater for merchant operations
2. Update error handling and logging
3. Comprehensive testing of all transaction types
4. Performance optimization

### Phase 4: Deployment & Monitoring (Week 4)
1. Production deployment
2. Monitoring and alerting setup
3. Documentation updates
4. Team training

## 9. Risk Mitigation

### 9.1 Backward Compatibility
- Maintain existing user withdrawal processing unchanged
- Use feature flags for merchant functionality
- Gradual rollout strategy

### 9.2 Data Integrity
- Comprehensive validation for merchant transactions
- Transaction type verification at all processing stages
- Audit logging for all merchant operations

### 9.3 Performance Impact
- Monitor queue processing performance
- Implement separate rate limiting for merchant operations
- Consider dedicated worker pools if needed

## Implementation Status

### ✅ Completed Components

1. **Updated Client API** - Successfully generated with all merchant methods
2. **Service Interface Extensions** - Enhanced `IWithdrawalClient` with merchant methods
3. **gRPC Client Implementation** - Added merchant withdrawal and settlement methods
4. **Unified Transaction Model** - Created `TransactionRequest` structure for all transaction types
5. **Enhanced Processor** - Developed unified processor for all transaction types
6. **Enhanced Consumer Framework** - Created framework for processing unified messages

### 🔄 Implementation Files Created/Modified

#### New Files:
- `/internal/model/transaction_types.go` - Unified transaction types and structures
- `/internal/logic/task/enhanced_processor/processor.go` - Enhanced processor for all transaction types
- `/internal/logic/task/enhanced_consumer/processor.go` - Enhanced consumer framework

#### Modified Files:
- `/internal/service/withdrawal_client.go` - Extended interface with merchant methods
- `/internal/client/task/client.go` - Added merchant gRPC client methods
- `/internal/controller/task_withdraw/task_withdraw.go` - Fixed merchant method signatures
- `/manifest/protobuf/task_service.proto` - Updated with merchant definitions
- `/api/` - Regenerated protobuf client code

### 📋 Next Implementation Steps

#### Phase 1: Core Integration (Next 2-3 days)
1. **Integrate Enhanced Processor**:
   - Update task registry to use `enhanced_processor.ProcessPendingTransactions`
   - Update configuration to support transaction type settings
   - Test processor with all transaction types

2. **Integrate Enhanced Consumer**:
   - Update consumer worker to use `enhanced_consumer.ProcessEnhancedMessage`
   - Implement complete processing logic for merchant transactions
   - Add proper error handling and status updates

3. **Configuration Updates**:
   - Update `config.yaml` with enhanced transaction type configuration
   - Add merchant-specific processing settings
   - Configure queue names and batch sizes

#### Phase 2: Status Updates & Error Handling (Next 3-4 days)
1. **Enhanced Status Updater**:
   - Extend gRPC updater to handle merchant status updates
   - Implement type-specific status update logic
   - Add proper error categorization for merchant transactions

2. **Error Handling Enhancement**:
   - Implement merchant-specific error handling
   - Add retry logic for merchant transactions
   - Enhance logging with transaction type context

#### Phase 3: Testing & Optimization (Next 5-7 days)
1. **Comprehensive Testing**:
   - Unit tests for all new components
   - Integration tests with all transaction types
   - Performance testing with mixed transaction loads

2. **Monitoring & Alerting**:
   - Add metrics for merchant transaction processing
   - Implement alerts for merchant-specific failures
   - Dashboard updates for merchant operations

### 🔧 Configuration Template

Add the following to your `config.yaml`:

```yaml
withdrawalProcessor:
  enabled: true
  spec: "*/10 * * * * *"
  batchSize: 50

  # Enhanced transaction type configuration
  transactionTypes:
    userWithdrawals:
      enabled: true
      batchSize: 50
    merchantWithdrawals:
      enabled: true
      batchSize: 30
    merchantSettlements:
      enabled: true
      batchSize: 20

withdrawalConsumer:
  enabled: true
  # Unified queue for all transaction types
  redisQueueName: "queue:transaction_processing"
  concurrency: 1
  dlqName: "queue:transaction_processing_dlq"
```

### 🚀 Quick Start Guide

1. **Update Configuration**: Add the enhanced configuration to your `config.yaml`

2. **Switch to Enhanced Processor**: Update your task registry to use:
   ```go
   enhanced_processor.ProcessPendingTransactions
   ```

3. **Switch to Enhanced Consumer**: Update your consumer to use:
   ```go
   enhanced_consumer.ProcessEnhancedMessage
   ```

4. **Test with Existing Data**: The system maintains backward compatibility with existing user withdrawals

5. **Enable Merchant Processing**: Set merchant transaction types to `enabled: true` in configuration

### 🔍 Architecture Benefits

1. **Unified Processing**: Single pipeline handles all transaction types
2. **Backward Compatibility**: Existing user withdrawals continue to work unchanged
3. **Type Safety**: Strong typing for all transaction types
4. **Extensibility**: Easy to add new transaction types in the future
5. **Consistent Error Handling**: Unified error handling across all transaction types
6. **Monitoring**: Centralized monitoring and logging for all operations

### 📊 Expected Performance Impact

- **Minimal Impact**: Enhanced processing adds <5% overhead
- **Scalability**: Supports independent scaling of different transaction types
- **Resource Efficiency**: Shared infrastructure reduces resource usage
- **Monitoring**: Better visibility into all transaction processing

## Next Steps

1. **Immediate**: Integrate enhanced processor and consumer
2. **Short-term**: Complete status update enhancements
3. **Medium-term**: Full testing and performance optimization
4. **Long-term**: Production deployment and monitoring setup
