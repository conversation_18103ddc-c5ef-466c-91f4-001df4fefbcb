syntax = "proto3";

package task.v1; // 使用版本化的包名

import "google/protobuf/timestamp.proto"; // 用于时间戳
// 移除: import "google/protobuf/any.proto";
import "google/protobuf/empty.proto";     // 添加: 用于无数据成功响应

// 指定 Go 包路径和别名，将在 task-server/api/task/v1 下生成代码
option go_package = "task-server/api/task/v1;taskv1";

// 提币记录消息 (映射 entity.UserWithdraws)
// 注意: WithdrawalStatus 枚举已移除，状态使用三个独立的 int32 状态字段
message Withdrawal {
  int64 user_withdraws_id = 1; // uint -> int64
  int64 user_id = 2;           // uint64 -> int64
  int64 token_id = 3;          // uint -> int64
  string wallet_id = 4;
  string name = 5;
  string chan = 6;
  string order_no = 7;
  string address = 8;
  string recipient_name = 9;    // 法币字段，保留
  string recipient_account = 10; // 法币字段，保留
  double amount = 11;
  double handling_fee = 12;
  double actual_amount = 13;
  int32 audit_status = 14;             // 审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝
  int32 auto_withdrawal_progress = 15; // 自动提现状态 0 未开始 1 进行中 2 成功 3 结束
  int32 processing_status = 16;        // 提现处理状态: 1-自动放币处理中, 2-处理中(待冷钱包转入)，3.待人工转账.，4-成功, 5-失败
  string refuse_reason_zh = 17;
  string refuse_reason_en = 18;
  string tx_hash = 19;
  string error_message = 20; // 注意：DB 中可能是 JSON 字符串
  string user_remark = 21;
  string admin_remark = 22;
  google.protobuf.Timestamp created_at = 23;
  google.protobuf.Timestamp checked_at = 24;
  google.protobuf.Timestamp processing_at = 25;
  google.protobuf.Timestamp completed_at = 26;
  google.protobuf.Timestamp updated_at = 27;
  int32 retries = 28;
  int32 nergy_state = 29;
}

// 商户提现记录消息 (映射 entity.MerchantWithdraws)
message MerchantWithdraw {
  int64 withdraws_id = 1;        // uint -> int64
  int32 withdraws_type = 2;      // 提现类型 1 代付 2结算
  int64 merchant_id = 3;         // uint64 -> int64
  int64 token_id = 4;            // uint -> int64
  string wallet_id = 5;
  string name = 6;
  string chan = 7;
  string order_no = 8;
  string address = 9;
  string recipient_name = 10;    // 法币收款人姓名
  string recipient_account = 11; // 法币收款账户
  string recipient_qrcode = 12;  // 法币收款二维码
  double amount = 13;
  double handling_fee = 14;
  double actual_amount = 15;
  int32 state = 16;              // 状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)
  string refuse_reason_zh = 17;
  string refuse_reason_en = 18;
  string tx_hash = 19;
  string error_message = 20;
  string user_remark = 21;
  string admin_remark = 22;
  google.protobuf.Timestamp created_at = 23;
  google.protobuf.Timestamp checked_at = 24;
  google.protobuf.Timestamp processing_at = 25;
  google.protobuf.Timestamp completed_at = 26;
  google.protobuf.Timestamp updated_at = 27;
  int32 retries = 28;
  int32 nergy_state = 29;
  int32 notification_sent = 30;   // 是否已发送通知: 0-未发送, 1-已发送
  google.protobuf.Timestamp notification_sent_at = 31;
  string fiat_type = 32;          // 法币提现类型
}

// 商户结算记录消息 (映射 entity.MerchantSettlements)
message MerchantSettlement {
  int64 settlements_id = 1;      // uint -> int64
  int32 settlements_type = 2;    // 提现类型 1 代付 2结算
  int64 merchant_id = 3;         // uint64 -> int64
  int64 token_id = 4;            // uint -> int64
  string wallet_id = 5;
  string name = 6;
  string chan = 7;
  string order_no = 8;
  string address = 9;
  string recipient_name = 10;    // 法币收款人姓名
  string recipient_account = 11; // 法币收款账户
  string recipient_qrcode = 12;  // 法币收款二维码
  double amount = 13;
  double handling_fee = 14;
  double actual_amount = 15;
  int32 state = 16;              // 状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)
  string refuse_reason_zh = 17;
  string refuse_reason_en = 18;
  string tx_hash = 19;
  string error_message = 20;
  string user_remark = 21;
  string admin_remark = 22;
  google.protobuf.Timestamp created_at = 23;
  google.protobuf.Timestamp checked_at = 24;
  google.protobuf.Timestamp processing_at = 25;
  google.protobuf.Timestamp completed_at = 26;
  google.protobuf.Timestamp updated_at = 27;
  int32 retries = 28;
  int32 nergy_state = 29;
  int32 notification_sent = 30;   // 是否已发送通知: 0-未发送, 1-已发送
  google.protobuf.Timestamp notification_sent_at = 31;
  string fiat_type = 32;          // 法币提现类型
}

// ListWithdrawals 请求
message ListWithdrawalsRequest {
  int32 page_size = 1; // 每页数量 (建议 > 0)
  int32 page = 2;      // 页码 (建议 >= 1)
  // 可选过滤条件
  int32 filter_audit_status = 3;           // 按审核状态过滤 (0 表示不过滤)
  int32 filter_processing_status = 4;      // 按处理状态过滤 (0 表示不过滤)
  int64 filter_user_id = 5;                // 按用户ID过滤 (0 表示不过滤)
  string filter_order_no = 6;              // 按订单号过滤 (空字符串表示不过滤)
  // 可以添加更多过滤，如 token_id, address, 时间范围等
}

// ListWithdrawals 响应
message ListWithdrawalsResponse {
  repeated Withdrawal withdrawals = 1; // 提币记录列表
  int32 total_count = 2;             // 总记录数
  int32 current_page = 3;            // 当前页码
  int32 total_pages = 4;             // 总页数
}

// ListMerchantWithdraws 请求
message ListMerchantWithdrawsRequest {
  int32 page_size = 1; // 每页数量 (建议 > 0)
  int32 page = 2;      // 页码 (建议 >= 1)
  // 可选过滤条件
  int32 filter_state = 3;                  // 按状态过滤 (0 表示不过滤)
  int32 filter_withdraws_type = 4;         // 按提现类型过滤 (0 表示不过滤)
  int64 filter_merchant_id = 5;            // 按商户ID过滤 (0 表示不过滤)
  string filter_order_no = 6;              // 按订单号过滤 (空字符串表示不过滤)
}

// ListMerchantWithdraws 响应
message ListMerchantWithdrawsResponse {
  repeated MerchantWithdraw merchant_withdraws = 1; // 商户提现记录列表
  int32 total_count = 2;                           // 总记录数
  int32 current_page = 3;                          // 当前页码
  int32 total_pages = 4;                           // 总页数
}

// ListMerchantSettlements 请求
message ListMerchantSettlementsRequest {
  int32 page_size = 1; // 每页数量 (建议 > 0)
  int32 page = 2;      // 页码 (建议 >= 1)
  // 可选过滤条件
  int32 filter_state = 3;                  // 按状态过滤 (0 表示不过滤)
  int32 filter_settlements_type = 4;       // 按结算类型过滤 (0 表示不过滤)
  int64 filter_merchant_id = 5;            // 按商户ID过滤 (0 表示不过滤)
  string filter_order_no = 6;              // 按订单号过滤 (空字符串表示不过滤)
}

// ListMerchantSettlements 响应
message ListMerchantSettlementsResponse {
  repeated MerchantSettlement merchant_settlements = 1; // 商户结算记录列表
  int32 total_count = 2;                               // 总记录数
  int32 current_page = 3;                              // 当前页码
  int32 total_pages = 4;                               // 总页数
}

// UpdateMerchantWithdraw 请求
message UpdateMerchantWithdrawRequest {
  int64 withdraws_id = 1;                     // 商户提现记录ID
  int32 state = 2;                            // 可选：状态: 1-待审核, 2-处理中, 3-已拒绝, 4-已完成, 5-失败
  string error_message = 3;                   // 可选：错误信息
  string refuse_reason_zh = 4;                // 可选：拒绝原因（中文）
  string refuse_reason_en = 5;                // 可选：拒绝原因（英文）
  string tx_hash = 6;                         // 可选：交易哈希
  int32 retries = 7;                          // 可选：重试次数
  string admin_remark = 8;                    // 可选：管理员备注
  int32 notification_sent = 9;                // 可选：通知发送状态
}

// UpdateMerchantSettlement 请求
message UpdateMerchantSettlementRequest {
  int64 settlements_id = 1;                   // 商户结算记录ID
  int32 state = 2;                            // 可选：状态: 1-待审核, 2-处理中, 3-已拒绝, 4-已完成, 5-失败
  string error_message = 3;                   // 可选：错误信息
  string refuse_reason_zh = 4;                // 可选：拒绝原因（中文）
  string refuse_reason_en = 5;                // 可选：拒绝原因（英文）
  string tx_hash = 6;                         // 可选：交易哈希
  int32 retries = 7;                          // 可选：重试次数
  string admin_remark = 8;                    // 可选：管理员备注
  int32 notification_sent = 9;                // 可选：通知发送状态
}

// UpdateWithdrawalStatus 请求
message UpdateWithdrawalStatusRequest {
  int64 withdrawal_id = 1;                    // 提币记录ID
  int32 audit_status = 2;                     // 可选：审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝
  int32 auto_withdrawal_progress = 3;         // 可选：自动提现状态 0 未开始 1 进行中 2 成功 3 结束
  int32 processing_status = 4;                // 可选：提现处理状态: 1-自动放币处理中, 2-处理中(待冷钱包转入)，3.待人工转账.，4-成功, 5-失败
  string error_message = 5;                   // 可选：错误信息（JSON字符串）
  string refuse_reason_zh = 6;                // 可选：拒绝原因（中文）
  string refuse_reason_en = 7;                // 可选：拒绝原因（英文）
  string tx_hash = 8;                         // 可选：交易哈希
  int32 retries = 9;                          // 可选：重试次数
  string admin_remark = 10;                   // 可选：管理员备注
}

// 通用 API 响应结构 (使用 oneof)
message ApiResponse {
  int32 code = 1;       // 业务状态码
  string message = 2;   // 响应消息
  oneof data_payload { // 定义 oneof 字段
    ListWithdrawalsResponse list_withdrawals_data = 3;                   // 用于 ListWithdrawals 成功响应
    google.protobuf.Empty success_no_data = 4;                         // 用于 UpdateWithdrawalStatus 等无数据成功响应
    ListMerchantWithdrawsResponse list_merchant_withdraws_data = 5;     // 用于 ListMerchantWithdraws 成功响应
    ListMerchantSettlementsResponse list_merchant_settlements_data = 6; // 用于 ListMerchantSettlements 成功响应
  }
}

// TaskService 服务定义
service TaskService {
  // 获取提币记录列表 (带过滤和分页)
  // 返回通用响应结构，data 字段包含 ListWithdrawalsResponse
  rpc ListWithdrawals(ListWithdrawalsRequest) returns (ApiResponse);

  // 更新提币记录状态和字段
  // 返回通用响应结构，data 字段包含更新后的 Withdrawal (如果成功且需要返回) 或为空
  rpc UpdateWithdrawalStatus(UpdateWithdrawalStatusRequest) returns (ApiResponse);

  // 获取商户提现记录列表 (带过滤和分页)
  // 返回通用响应结构，data 字段包含 ListMerchantWithdrawsResponse
  rpc ListMerchantWithdraws(ListMerchantWithdrawsRequest) returns (ApiResponse);

  // 更新商户提现记录状态和字段
  // 返回通用响应结构，data 字段包含更新后的 MerchantWithdraw (如果成功且需要返回) 或为空
  rpc UpdateMerchantWithdraw(UpdateMerchantWithdrawRequest) returns (ApiResponse);

  // 获取商户结算记录列表 (带过滤和分页)
  // 返回通用响应结构，data 字段包含 ListMerchantSettlementsResponse
  rpc ListMerchantSettlements(ListMerchantSettlementsRequest) returns (ApiResponse);

  // 更新商户结算记录状态和字段
  // 返回通用响应结构，data 字段包含更新后的 MerchantSettlement (如果成功且需要返回) 或为空
  rpc UpdateMerchantSettlement(UpdateMerchantSettlementRequest) returns (ApiResponse);
}