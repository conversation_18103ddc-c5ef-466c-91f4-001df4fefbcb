# 商户提现真实交易实现指南

## 当前状态：模拟交易

### 为什么是 "simulated_tx_hash"？

当前商户提现使用**模拟交易**，原因如下：

1. **循环导入限制**：
   ```
   withdrawal_consumer → 导入 enhanced_consumer（处理商户交易）
   enhanced_consumer → 不能导入 withdrawal_consumer（会造成循环）
   ```

2. **函数访问限制**：
   - `withdrawal_processor` 包中的处理函数是私有的（小写开头）
   - 无法从 `enhanced_consumer` 直接调用这些函数

### 当前的处理流程：

```go
// enhanced_consumer/processor.go 第114行
statusErr := merchant_handler.PushMerchantStatusUpdate(
    ctx, 
    "merchant_withdrawal", 
    withdrawal.WithdrawsId, 
    4,                    // 固定状态：成功
    "simulated_tx_hash",  // 模拟的交易哈希
    "", 
    0
)
```

## 实现真实交易的方案

### 方案1：重构包结构（推荐）

创建独立的交易处理包，避免循环导入：

```
internal/
  logic/
    transaction/          # 新建独立的交易处理包
      processor.go        # 通用交易处理逻辑
      eth_handler.go      # ETH交易处理
      tron_handler.go     # TRON交易处理
    
    withdrawal_consumer/  # 用户提现
      processor.go        → 调用 transaction 包
    
    enhanced_consumer/    # 商户交易
      processor.go        → 调用 transaction 包
```

#### 实现步骤：

1. **创建 transaction 包**：
```go
// internal/logic/transaction/processor.go
package transaction

import (
    "context"
    "github.com/shopspring/decimal"
    // ...
)

// ProcessBlockchainTransaction 处理区块链交易
func ProcessBlockchainTransaction(
    ctx context.Context,
    blockchain string,
    tokenKey string,
    amount decimal.Decimal,
    toAddress string,
    // ... 其他参数
) (txHash string, err error) {
    // 实际的区块链交易逻辑
    switch blockchain {
    case "ETH":
        return processETHTransaction(...)
    case "TRON":
        return processTRONTransaction(...)
    }
}
```

2. **修改 enhanced_consumer**：
```go
// enhanced_consumer/processor.go
func processMerchantWithdrawalSimple(...) {
    // 转换格式
    userWithdrawal := convertMerchantWithdrawalToUser(withdrawal)
    
    // 调用独立的交易处理包
    txHash, err := transaction.ProcessBlockchainTransaction(
        ctx,
        userWithdrawal.Chan,     // 区块链类型
        userWithdrawal.Name,     // 代币类型
        userWithdrawal.ActualAmount,
        userWithdrawal.Address,
        // ...
    )
    
    // 根据结果设置状态
    var finalState int32
    var errorMessage string
    
    if err != nil {
        if strings.Contains(err.Error(), "insufficient") {
            finalState = 2  // 余额不足，等待重试
        } else {
            finalState = 5  // 失败
        }
        errorMessage = err.Error()
    } else {
        finalState = 4  // 成功
    }
    
    // 推送真实的状态更新
    merchant_handler.PushMerchantStatusUpdate(
        ctx, 
        "merchant_withdrawal", 
        withdrawal.WithdrawsId, 
        finalState, 
        txHash,      // 真实的交易哈希
        errorMessage,
        withdrawal.Retries
    )
}
```

### 方案2：导出必要函数

修改 `withdrawal_processor` 包，将关键函数导出（改为大写开头）：

```go
// withdrawal_processor/withdrawal_processing_steps.go

// 原来：func deriveWithdrawalInfo(...)
// 改为：
func DeriveWithdrawalInfo(withdrawal *taskv1.Withdrawal) (...) {
    // ...
}

// 原来：func sendTransaction(...)
// 改为：
func SendTransaction(
    ctx context.Context,
    sender BlockchainSender,
    // ...
) (txHash string, err error) {
    // ...
}
```

然后在 `enhanced_consumer` 中直接调用：

```go
// 可以直接调用导出的函数
derivedInfo, err := wp.DeriveWithdrawalInfo(userWithdrawal)
txHash, err := wp.SendTransaction(ctx, sender, ...)
```

### 方案3：使用接口抽象

定义通用的交易处理接口：

```go
// internal/service/transaction.go
type TransactionProcessor interface {
    ProcessWithdrawal(ctx context.Context, withdrawal interface{}) (txHash string, err error)
}

// 为用户提现和商户提现分别实现
type UserWithdrawalProcessor struct {}
type MerchantWithdrawalProcessor struct {}
```

## 测试真实交易

### 1. 本地测试环境

使用测试网络进行测试：
- ETH: Goerli/Sepolia 测试网
- TRON: Nile/Shasta 测试网

### 2. 配置测试钱包

```yaml
wallets:
  ETH:
    privateKey: "测试网私钥"
    address: "测试地址"
  TRON:
    privateKey: "测试网私钥"  
    address: "测试地址"
```

### 3. 验证交易

检查数据库中的交易哈希：
```sql
SELECT withdraws_id, state, tx_hash, updated_at 
FROM merchant_withdraws 
WHERE withdraws_id = 1;
```

验证区块链上的交易：
- ETH: https://goerli.etherscan.io/tx/{tx_hash}
- TRON: https://nile.tronscan.org/#/transaction/{tx_hash}

## 状态映射

商户交易状态对应关系：

| 状态值 | 含义 | 说明 |
|-------|------|------|
| 1 | 待审核 | 初始状态，不处理 |
| 2 | 处理中 | 正在处理或余额不足等待重试 |
| 3 | 已拒绝 | 审核拒绝 |
| 4 | 已完成 | 交易成功 |
| 5 | 失败 | 交易失败 |

## 总结

当前的 `simulated_tx_hash` 是为了：
1. 验证商户提现流程是否正确
2. 确保更新正确的数据库表
3. 避免编译错误

在生产环境中，应该：
1. 采用上述方案之一实现真实交易
2. 返回真实的区块链交易哈希
3. 正确处理各种错误状态（余额不足、网络错误等）