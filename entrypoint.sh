#!/bin/bash

json_file_path="${PATH_TO_SECRET_FILE}"

# Check if the JSON file exists
if [ ! -f "$json_file_path" ]; then
    echo "JSON file doesn't exists"
    exit 1
fi

temp_file=$(mktemp)
# Extract key-value pairs from the JSON and store them in the temporary file
jq -r 'to_entries[] | "\(.key)=\(.value | @json)"' "$json_file_path" > "$temp_file"

# Export values from the JSON response
while IFS="=" read -r key value; do
    # Remove quotes only at the beginning and end of the string
    clean_value=$(echo "$value" | sed 's/^"\(.*\)"$/\1/')
    export "$key"="$clean_value"
done < "$temp_file"

# Remove the temporary file
rm "$temp_file"

envsubst < ./manifest/config/config.yaml.template > ./manifest/config/config.yaml


# Execute
# Debug: Print the command to be executed
echo "Executing command: $@"
# Execute the main application binary using the APP_NAME variable
exec "$@"
