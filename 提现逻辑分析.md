# 提现系统详细逻辑分析

## 一、系统架构概述

提现系统采用生产者-消费者模式，通过Redis队列解耦任务获取和处理，主要包含两个核心组件：

1. **Withdrawal Processor（提现处理器）**：负责从gRPC接口获取待处理提现任务并推送到Redis队列
2. **Withdrawal Consumer（提现消费者）**：负责从Redis队列消费提现任务并执行实际的区块链转账操作

## 二、详细处理流程

### 2.1 提现任务获取（Withdrawal Processor）

**文件位置**：`internal/logic/task/withdrawal_processor/processor.go`

#### 处理步骤：
1. **ProcessPendingWithdrawals函数**定期执行（由task registry调度）
2. 通过gRPC调用`GetPendingWithdrawalsStream`获取待处理的提现记录
3. 每获取一条记录，立即推送到Redis队列（`withdrawal_queue`）
4. 推送格式：JSON序列化的`WithdrawalRequest`对象

### 2.2 提现任务消费（Withdrawal Consumer）

**文件位置**：`internal/logic/task/withdrawal_consumer/`

#### 启动流程：
1. **StartConsumers函数**根据配置的并发数启动多个Worker
2. 每个Worker运行独立的goroutine，执行**RunWorker函数**
3. Worker使用`BRPOP`命令从Redis队列阻塞式获取任务
4. 获取到任务后调用**ProcessMessage函数**处理

### 2.3 核心处理逻辑（ProcessMessage）

**文件位置**：`internal/logic/task/withdrawal_consumer/processor.go`

#### 具体处理步骤：

##### 步骤1：解析提现信息（DeriveWithdrawalInfo）
```go
// 从请求中提取关键信息
- symbol: 代币符号（如ETH、USDT）
- chainType: 链类型（ETH或TRON）
- tokenStandard: 代币标准（native、erc20、trc20）
- tokenConfigKey: 配置键（如"USDT_erc20"）
```

##### 步骤2：检查TRC20能量状态（CheckNergyState）
```go
// 仅对TRC20代币执行，当前代码中被注释掉
// 原本用于检查TRON网络的能量状态
// 当前直接返回skip=true，跳过此步骤
```

##### 步骤3：获取代币配置（GetTokenConfig）
```go
// 从配置中获取：
- contractAddress: 代币合约地址（原生代币为空）
- precision: 代币精度（小数位数）
// 配置来源：tokenContracts和tokenPrecisions映射表
```

##### 步骤4：检查代币是否启用（CheckTokenEnabled）
```go
// 检查withdrawalEnabledTokens配置
// 支持两种格式：
- 仅symbol（如"USDT"）：所有标准都启用
- symbol_standard（如"USDT_trc20"）：特定标准启用
```

##### 步骤5：验证收款地址（ValidateAddress）
```go
// ETH地址验证：
- 长度：42位
- 格式：0x开头的十六进制字符串

// TRON地址验证：
- 长度：34位
- 格式：T开头的Base58编码字符串
```

##### 步骤6：验证金额和限额（ValidateAmountAndLimit）
```go
// 金额验证：
- 必须大于0

// 限额验证：
- 使用MemoryRateLimiter检查单笔限额
- 限额键：chainType_tokenStandard（如"ETH_native"）
- 检查当前金额是否超过配置的单笔限额
```

##### 步骤7：选择发送器并检查余额（SelectSenderAndCheckBalance）
```go
// 根据chainType选择对应的发送器：
- ETH链：使用ETH发送器
- TRON链：使用TRON发送器

// 余额检查：
- 原生代币：直接查询地址余额
- ERC20/TRC20：查询代币合约余额
- 确保余额充足（包含手续费）
```

##### 步骤8：发送交易（SendTransaction）
```go
// 调用具体链的发送器执行转账
// 返回交易哈希（txHash）
```

### 2.4 状态更新机制

#### 成功处理：
1. 推送状态更新到Redis（状态码：4 - PROCESSING）
2. 包含交易哈希信息

#### 错误处理：
1. **可重试错误**：
   - 余额不足
   - 网络错误
   - 资源不足（TRON）
   - 推送状态：1（PENDING），等待重试

2. **不可重试错误**（配置在NonRetryableErrors中）：
   - 地址格式错误
   - 配置错误
   - 验证失败
   - 推送状态：5（FAILED）

3. **达到最大重试次数**：
   - 默认最大重试：3次
   - 错误历史记录在ErrorMessage字段（JSON数组）
   - 移动到死信队列（DLQ）

## 三、区块链发送器实现

### 3.1 ETH发送器

**文件位置**：`internal/logic/task/withdrawal_processor/sender/eth/`

#### 核心功能：

##### 1. 初始化（NewEthSender）
- 连接以太坊RPC节点
- 加载私钥（支持配置文件或环境变量）
- 验证地址匹配
- 加载ERC20 ABI

##### 2. 发送交易（SendTransaction）
```go
处理流程：
1. 准备交易输入（prepareTxInput）
   - 验证地址格式
   - 处理金额精度
   - 计算value值

2. 构建未签名交易（buildUnsignedTx）
   - 获取nonce（支持Redis和RPC策略）
   - 设置gas价格（支持fixed和node_suggested策略）
   - 估算gas限制
   - 检查费用是否超限

3. 签名并发送（signAndSend）
   - 使用私钥签名
   - 广播交易
   - 返回交易哈希
```

##### 3. 余额查询（GetBalance）
- ETH：直接查询地址余额
- ERC20：调用合约balanceOf方法

### 3.2 TRON发送器

**文件位置**：`internal/logic/task/withdrawal_processor/sender/tron/`

#### 核心功能：

##### 1. 初始化（NewTronSender）
- 连接TRON gRPC节点
- 加载私钥和派生地址
- 设置API Key（如果配置）
- 60秒连接超时

##### 2. 发送交易（SendTransaction）
```go
处理流程：
1. 特殊检查（仅TRC20 USDT）
   - 检查账户能量（最小6500）
   - 检查账户带宽（最小350）
   - 不足时返回ErrInsufficientTronResources

2. 创建交易
   - TRX：使用CreateTransaction
   - TRC20：使用TRC20Send

3. 签名并广播
   - 使用私钥签名
   - 广播到TRON网络
   - 返回交易哈希
```

##### 3. 余额查询（GetBalance）
- TRX：使用GetAccount查询
- TRC20：使用TRC20ContractBalance查询

## 四、配置结构

### 4.1 Consumer配置
```yaml
consumer:
  concurrency: 5                    # 并发Worker数量
  queueName: "withdrawal_queue"     # Redis队列名
  dlqName: "withdrawal_dlq"         # 死信队列名
```

### 4.2 Processor配置
```yaml
processor:
  # 限额配置
  singleTransactionLimits:
    ETH_native: "10"
    ETH_erc20: "50000"
    TRON_native: "100000"
    TRON_trc20: "50000"
  
  # 钱包配置
  ethWallet:
    privateKey: "xxx"              # 或使用privateKeyEnv
    address: "0x..."
  tronWallet:
    privateKey: "xxx"
    address: "T..."
  
  # RPC配置
  ethRpcUrl: "https://..."
  tronGrpcEndpoint: "grpc.trongrid.io:50051"
  tronApiKey: "xxx"
  
  # 交易参数
  ethGasPrice: "20"                # Gwei
  ethGasLimit: 100000
  ethMaxTotalFee: "0.01"           # ETH
  tronFeeLimit: *********          # Sun
  
  # 代币配置
  tokenContracts:
    USDT_erc20: "0x..."
    USDT_trc20: "T..."
  tokenPrecisions:
    ETH: 18
    USDT: 6
    TRON: 6
  
  # 启用的代币
  withdrawalEnabledTokens:
    - "ETH"
    - "USDT_erc20"
    - "USDT_trc20"
    - "TRON"
  
  # 重试配置
  maxRetryAttempts: 3
  nonRetryableErrors:
    - "invalid address format"
    - "token config not found"
```

## 五、关键特性总结

1. **高可用性**：
   - 多Worker并发处理
   - Redis队列持久化
   - 完善的错误处理和重试机制

2. **安全性**：
   - 私钥加密存储
   - 地址格式验证
   - 金额和限额检查
   - 余额预检查

3. **可扩展性**：
   - 支持多链（ETH、TRON）
   - 支持多种代币标准
   - 配置驱动，易于添加新币种

4. **可维护性**：
   - 清晰的模块划分
   - 完整的错误日志
   - 死信队列便于问题排查

5. **性能优化**：
   - 批量获取任务
   - 并发处理
   - Redis缓存nonce（ETH）

## 六、异常处理流程图

```mermaid
graph TD
    A[处理提现任务] --> B{处理是否成功?}
    B -->|是| C[推送PROCESSING状态]
    B -->|否| D{是否可重试错误?}
    D -->|是| E{是否达到最大重试?}
    D -->|否| F[推送FAILED状态]
    E -->|否| G[推送PENDING状态<br/>等待重试]
    E -->|是| H[推送FAILED状态<br/>移到死信队列]
```

## 七、数据流向图

```mermaid
graph LR
    A[gRPC服务] -->|获取待处理提现| B[Withdrawal Processor]
    B -->|推送任务| C[Redis Queue]
    C -->|BRPOP获取| D[Withdrawal Consumer]
    D -->|验证和检查| E{处理步骤1-7}
    E -->|通过| F[区块链发送器]
    E -->|失败| G[错误处理]
    F -->|ETH| H[ETH Sender]
    F -->|TRON| I[TRON Sender]
    H --> J[交易完成]
    I --> J
    J --> K[更新状态到Redis]
    G --> L[重试/失败/DLQ]
```

## 八、注意事项

1. **TRC20 USDT特殊处理**：需要检查能量和带宽资源
2. **精度处理**：不同代币有不同的小数位数
3. **Nonce管理**：ETH需要管理nonce，TRON自动处理
4. **费用控制**：ETH有最大费用限制检查
5. **地址格式**：ETH和TRON地址格式完全不同
6. **错误分类**：正确区分可重试和不可重试错误