# 商户提现修复测试报告

## 修复内容总结

### 1. 解决的问题
- **原问题**：商户提现订单被当作用户提现处理，导致 `withdrawal Name (symbol) is empty` 错误
- **根本原因**：系统使用 `withdrawal_consumer.ProcessMessage` 直接处理所有消息，无法区分商户和用户提现

### 2. 实施的修复

#### A. 消息路由机制
```go
// 在 withdrawal_consumer/worker.go 中添加判断
if isTransactionRequest(message) {
    // 商户交易：使用 enhanced_consumer 处理
    enhanced_consumer.ProcessEnhancedMessage(...)
} else {
    // 用户提现：使用原有逻辑
    ProcessMessage(...)
}
```

#### B. 商户交易处理器
- 创建 `enhanced_consumer/processor.go` 处理商户交易
- 将商户提现/结算转换为用户提现格式
- 处理后推送到商户状态更新队列

#### C. 商户状态更新系统
- `merchant_handler/merchant_status_handler.go`：处理状态更新
- `merchant_handler/merchant_status_worker.go`：后台工作线程
- 确保更新正确的数据库表（`merchant_withdraws` 而非 `user_withdraws`）

### 3. 关键改进

1. **正确的消息路由**
   - 商户交易 → enhanced_consumer → 商户状态更新队列 → merchant_withdraws 表
   - 用户提现 → withdrawal_consumer → 用户状态更新队列 → user_withdraws 表

2. **状态过滤修复**
   - 商户结算现在获取 `state=2`（处理中）而非 `state=1`（待审核）

3. **避免循环导入**
   - 重构代码结构，避免 withdrawal_consumer 和 enhanced_consumer 之间的循环依赖

## 测试验证

### 测试场景1：商户提现处理
**输入**：
- 商户提现订单，state=2，金额=10 TRX

**期望结果**：
- 订单被正确识别为商户提现
- 转换为用户提现格式处理
- 更新 merchant_withdraws 表的状态

### 测试场景2：商户结算处理
**输入**：
- 商户结算订单，state=2

**期望结果**：
- 订单被正确识别为商户结算
- 处理流程与商户提现相同
- 更新 merchant_settlements 表

### 测试场景3：用户提现不受影响
**输入**：
- 普通用户提现订单

**期望结果**：
- 继续使用原有处理逻辑
- 更新 user_withdraws 表

## 配置要求

```yaml
# 在 config.yaml 中启用商户交易处理
withdrawalProcessor:
  transactionTypes:
    merchantWithdrawals:
      enabled: true
      batchSize: 30
    merchantSettlements:
      enabled: true
      batchSize: 20
```

## 运行命令

```bash
# 编译
go build -o task-withdraw .

# 运行
./task-withdraw task
```

## 监控点

1. **日志关键词**：
   - `[MerchantWithdrawals]`：商户提现处理
   - `[MerchantSettlements]`：商户结算处理
   - `[MerchantStatusWorker]`：状态更新工作线程
   - `[EnhancedTransactionProcessor]`：增强处理器

2. **Redis队列**：
   - `queue:transaction_processing`：交易处理队列
   - `queue:merchant_status_update`：商户状态更新队列

## 已知限制

由于避免循环导入，当前enhanced_consumer中的商户交易处理是简化版本：
- 模拟成功交易（返回 state=4）
- 实际生产环境需要完整实现区块链交易逻辑

## 下一步建议

1. 实现完整的商户交易处理逻辑
2. 添加单元测试覆盖各种场景
3. 实现交易失败和重试机制
4. 添加监控指标和告警